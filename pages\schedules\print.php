<?php
/**
 * صفحة طباعة جدول الحراسة
 */

require_once '../../config/database.php';

$database = new Database();
$conn = $database->getConnection();

$scheduleId = $_GET['id'] ?? null;

if (!$scheduleId) {
    header('Location: index.php');
    exit();
}

// جلب بيانات الجدول
try {
    $stmt = $conn->prepare("SELECT * FROM guard_schedules WHERE id = ?");
    $stmt->execute([$scheduleId]);
    $schedule = $stmt->fetch();
    
    if (!$schedule) {
        header('Location: index.php');
        exit();
    }
} catch(PDOException $e) {
    die('خطأ في جلب بيانات الجدول: ' . $e->getMessage());
}

// جلب التعيينات
try {
    $stmt = $conn->prepare("
        SELECT ga.*, g.first_name, g.last_name, g.guard_number, g.institution 
        FROM guard_assignments ga 
        JOIN guards g ON ga.guard_id = g.id 
        WHERE ga.schedule_id = ? 
        ORDER BY ga.room_number, ga.guard_type, ga.guard_order
    ");
    $stmt->execute([$scheduleId]);
    $assignments = $stmt->fetchAll();
} catch(PDOException $e) {
    die('خطأ في جلب التعيينات: ' . $e->getMessage());
}

// تنظيم التعيينات حسب القاعة
$roomAssignments = [];
foreach ($assignments as $assignment) {
    $roomAssignments[$assignment['room_number']][] = $assignment;
}

// جلب إعدادات المركز
try {
    $stmt = $conn->prepare("SELECT * FROM center_settings LIMIT 1");
    $stmt->execute();
    $centerSettings = $stmt->fetch() ?: [];
} catch(PDOException $e) {
    $centerSettings = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة جدول الحراسة - <?= htmlspecialchars($schedule['schedule_name']) ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #10b981;
            --accent-color: #f59e0b;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --border-color: #e2e8f0;
            --border-radius: 8px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: white;
            color: var(--dark-color);
            line-height: 1.4;
            direction: rtl;
            padding: 15px;
            font-size: 14px;
        }

        .print-container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
        }

        .print-header {
            text-align: center;
            margin-bottom: 20px;
            padding: 20px;
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            color: white;
            border-radius: var(--border-radius);
            position: relative;
        }

        .header-logo {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 2rem;
        }

        .header-content {
            margin: 0 auto;
            max-width: 600px;
        }

        .ministry-name {
            font-size: 0.9rem;
            font-weight: 500;
            margin-bottom: 5px;
            opacity: 0.9;
        }

        .institution-name {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .print-title {
            font-size: 1.4rem;
            font-weight: 800;
            margin-bottom: 8px;
        }

        .print-subtitle {
            font-size: 1.1rem;
            margin-bottom: 10px;
            opacity: 0.95;
        }

        .academic-year {
            font-size: 0.9rem;
            font-weight: 500;
            opacity: 0.9;
        }

        .schedule-info {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
            background: var(--light-color);
            padding: 15px;
            border-radius: var(--border-radius);
        }

        .info-item {
            text-align: center;
            padding: 5px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }

        .info-label {
            font-weight: 600;
            color: var(--dark-color);
            font-size: 0.8rem;
            display: block;
            margin-bottom: 3px;
        }

        .info-value {
            color: var(--primary-color);
            font-weight: 500;
            font-size: 0.9rem;
        }

        .table-container {
            border: 2px solid var(--primary-color);
            border-radius: var(--border-radius);
            overflow: hidden;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .table-header {
            background: var(--primary-color);
            color: white;
            padding: 10px;
            text-align: center;
            font-weight: 700;
            font-size: 0.9rem;
        }

        .guards-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.75rem;
            background: white;
        }

        .guards-table th,
        .guards-table td {
            padding: 8px 4px;
            text-align: center;
            border: 1px solid #ddd;
            vertical-align: middle;
        }

        .guards-table th {
            background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
            color: white;
            font-weight: 600;
            font-size: 0.8rem;
            padding: 10px 4px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .guards-table tbody tr:nth-child(even) {
            background: #f8fafc;
        }

        .guards-table tbody tr:hover {
            background: #e0f2fe;
        }

        .room-number {
            font-weight: 700;
            color: white;
            background: linear-gradient(135deg, var(--primary-color), #1d4ed8) !important;
            font-size: 0.9rem;
            width: 70px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .guard-info {
            text-align: center;
            line-height: 1.3;
            padding: 4px;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.8);
        }

        .main-guard .guard-info {
            background: linear-gradient(135deg, #fef3c7, #fbbf24);
            border: 1px solid #f59e0b;
        }

        .regular-guard .guard-info {
            background: linear-gradient(135deg, #e0f2fe, #0ea5e9);
            border: 1px solid #0284c7;
        }

        .guard-name {
            font-weight: 700;
            margin-bottom: 3px;
            font-size: 0.75rem;
            color: #1f2937;
        }

        .guard-number {
            font-size: 0.65rem;
            color: #4b5563;
            margin-bottom: 2px;
            font-weight: 500;
        }

        .guard-institution {
            font-size: 0.6rem;
            color: #6b7280;
            font-weight: 400;
        }

        .empty-cell {
            color: #9ca3af;
            font-style: italic;
            font-size: 0.7rem;
            padding: 8px;
            background: #f9fafb;
            border-radius: 4px;
        }

        .stats-summary {
            background: var(--light-color);
            border-radius: var(--border-radius);
            padding: 12px;
            margin-bottom: 15px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
        }

        .stat-item {
            text-align: center;
            padding: 8px;
            background: white;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
        }

        .stat-number {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 3px;
        }

        .stat-label {
            font-size: 0.7rem;
            color: #666;
        }

        .notes-section {
            background: #fef3c7;
            border: 1px solid var(--accent-color);
            border-radius: var(--border-radius);
            padding: 10px;
            margin-top: 10px;
        }

        .notes-title {
            color: #92400e;
            margin-bottom: 5px;
            font-weight: 600;
            font-size: 0.8rem;
        }

        .notes-content {
            color: #92400e;
            font-size: 0.7rem;
        }

        .print-actions {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: var(--light-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
        }

        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 0 8px;
            border: none;
            border-radius: var(--border-radius);
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            text-align: center;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-success {
            background: var(--secondary-color);
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .print-footer {
            text-align: center;
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px solid var(--border-color);
            color: #666;
            font-size: 0.7rem;
        }

        /* Print-specific styles */
        @media print {
            .print-actions {
                display: none !important;
            }

            body {
                padding: 0.5cm !important;
                background: white !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                font-size: 12px !important;
            }

            .print-container {
                max-width: none;
                margin: 0;
            }

            .print-header {
                margin-bottom: 15px;
                padding: 15px !important;
                background: #2563eb !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .ministry-name {
                font-size: 0.7rem !important;
            }

            .institution-name {
                font-size: 0.8rem !important;
            }

            .print-title {
                font-size: 1.1rem !important;
                margin-bottom: 5px;
            }

            .print-subtitle {
                font-size: 0.9rem !important;
                margin-bottom: 5px;
            }

            .academic-year {
                font-size: 0.7rem !important;
            }

            .table-container {
                border: 2px solid #2563eb !important;
                box-shadow: none !important;
            }

            .table-header {
                background: #2563eb !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .schedule-info {
                margin-bottom: 15px;
                padding: 10px;
            }

            .guards-table {
                page-break-inside: auto;
                margin-bottom: 10px;
                font-size: 0.65rem !important;
            }

            .guards-table th,
            .guards-table td {
                padding: 4px 2px !important;
            }

            .guard-name {
                font-size: 0.65rem !important;
            }

            .guard-number {
                font-size: 0.55rem !important;
            }

            .guard-institution {
                font-size: 0.5rem !important;
            }

            .guards-table th {
                background: #2563eb !important;
                color: white !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .room-number {
                background: #2563eb !important;
                color: white !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .main-guard .guard-info {
                background: #fef3c7 !important;
                border: 1px solid #f59e0b !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .regular-guard .guard-info {
                background: #e0f2fe !important;
                border: 1px solid #0284c7 !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .stats-summary {
                page-break-inside: avoid;
                margin-bottom: 15px;
                padding: 10px;
            }

            .notes-section {
                page-break-inside: avoid;
                margin-top: 10px;
                padding: 8px;
            }

            .print-footer {
                margin-top: 10px;
                padding-top: 8px;
            }
        }

        @page {
            margin: 0.8cm;
            size: A4 portrait;
        }
    </style>
</head>
<body>
    <div class="print-container">
        <div class="print-actions">
            <h3 style="margin-bottom: 20px; color: var(--primary-color);">🖨️ طباعة جدول الحراسة</h3>
            <p style="color: #666; margin-bottom: 20px;">اضغط على زر الطباعة أدناه أو استخدم Ctrl+P لطباعة الجدول</p>
            <button onclick="window.print()" class="btn btn-primary">🖨️ طباعة الجدول</button>
            <a href="view.php?id=<?= $scheduleId ?>" class="btn btn-secondary">👁️ عرض الجدول</a>
            <a href="index.php" class="btn btn-success">📋 قائمة الجداول</a>
        </div>

        <div class="print-header">
            <div class="header-logo">🏛️</div>
            <div class="header-content">
                <div class="ministry-name">الجمهورية الجزائرية الديمقراطية الشعبية</div>
                <div class="ministry-name">وزارة التربية الوطنية</div>
                <div class="institution-name">مديرية التربية لولاية ...</div>
                <div class="print-title">جدول حراسة الامتحانات الرسمية</div>
                <div class="print-subtitle"><?= htmlspecialchars($schedule['schedule_name']) ?></div>
                <div class="academic-year">السنة الدراسية: <?= date('Y') ?>/<?= date('Y') + 1 ?></div>
            </div>
        </div>

        <div class="schedule-info">
            <div class="info-item">
                <span class="info-label">تاريخ الامتحان:</span>
                <span class="info-value"><?= date('Y-m-d', strtotime($schedule['exam_date'])) ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">فترة الامتحان:</span>
                <span class="info-value"><?= $schedule['exam_period'] == 'morning' ? 'الصباحية' : 'المسائية' ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">نوع الامتحان:</span>
                <span class="info-value">
                    <?= $schedule['exam_type'] == 'bem' ? 'شهادة التعليم المتوسط' : 'شهادة البكالوريا' ?>
                </span>
            </div>
            <div class="info-item">
                <span class="info-label">تاريخ الطباعة:</span>
                <span class="info-value"><?= date('Y-m-d H:i:s') ?></span>
            </div>
        </div>

        <?php
        // حساب الإحصائيات
        $totalAssignments = count($assignments);
        $mainGuardsCount = count(array_filter($assignments, function($a) { return $a['guard_type'] == 'main'; }));
        $regularGuardsCount = count(array_filter($assignments, function($a) { return $a['guard_type'] == 'regular'; }));
        $roomsWithGuards = count($roomAssignments);
        ?>

        <div class="stats-summary">
            <h4 style="margin-bottom: 10px; color: var(--primary-color); text-align: center; font-size: 0.9rem;">📊 ملخص الإحصائيات</h4>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number"><?= $totalAssignments ?></div>
                    <div class="stat-label">إجمالي التعيينات</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= $mainGuardsCount ?></div>
                    <div class="stat-label">حراس رئيسيين</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= $regularGuardsCount ?></div>
                    <div class="stat-label">حراس عاديين</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= $roomsWithGuards ?></div>
                    <div class="stat-label">قاعات مُعيَّنة</div>
                </div>
            </div>
        </div>

        <div class="table-container">
            <div class="table-header">
                📋 توزيع الحراس على القاعات
            </div>
            <table class="guards-table">
                <thead>
                    <tr>
                        <th style="width: 80px;">رقم القاعة</th>
                        <th style="width: 30%;">الحارس الرئيسي</th>
                        <th style="width: 30%;">الحارس العادي الأول</th>
                        <th style="width: 30%;">الحارس العادي الثاني</th>
                    </tr>
                </thead>
            <tbody>
                <?php for ($room = 1; $room <= ($centerSettings['rooms_count'] ?? 14); $room++): ?>
                <?php
                $roomNumber = str_pad($room, 2, '0', STR_PAD_LEFT);
                $roomGuards = $roomAssignments[$roomNumber] ?? [];

                $mainGuard = null;
                $guard2 = null;
                $guard3 = null;

                foreach ($roomGuards as $guard) {
                    if ($guard['guard_type'] == 'main') {
                        $mainGuard = $guard;
                    } elseif ($guard['guard_type'] == 'regular') {
                        if ($guard['guard_order'] == 2) $guard2 = $guard;
                        if ($guard['guard_order'] == 3) $guard3 = $guard;
                    }
                }
                ?>
                <tr>
                    <td class="room-number"><?= $roomNumber ?></td>

                    <td>
                        <?php if ($mainGuard): ?>
                        <div class="guard-info">
                            <div class="guard-name"><?= htmlspecialchars($mainGuard['first_name'] . ' ' . $mainGuard['last_name']) ?></div>
                            <div class="guard-number">رقم: <?= htmlspecialchars($mainGuard['guard_number']) ?></div>
                            <div class="guard-institution"><?= htmlspecialchars($mainGuard['institution']) ?></div>
                        </div>
                        <?php else: ?>
                        <div class="empty-cell">غير معيَّن</div>
                        <?php endif; ?>
                    </td>

                    <td>
                        <?php if ($guard2): ?>
                        <div class="guard-info">
                            <div class="guard-name"><?= htmlspecialchars($guard2['first_name'] . ' ' . $guard2['last_name']) ?></div>
                            <div class="guard-number">رقم: <?= htmlspecialchars($guard2['guard_number']) ?></div>
                            <div class="guard-institution"><?= htmlspecialchars($guard2['institution']) ?></div>
                        </div>
                        <?php else: ?>
                        <div class="empty-cell">غير معيَّن</div>
                        <?php endif; ?>
                    </td>

                    <td>
                        <?php if ($guard3): ?>
                        <div class="guard-info">
                            <div class="guard-name"><?= htmlspecialchars($guard3['first_name'] . ' ' . $guard3['last_name']) ?></div>
                            <div class="guard-number">رقم: <?= htmlspecialchars($guard3['guard_number']) ?></div>
                            <div class="guard-institution"><?= htmlspecialchars($guard3['institution']) ?></div>
                        </div>
                        <?php else: ?>
                        <div class="empty-cell">غير معيَّن</div>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endfor; ?>
            </tbody>
            </table>
        </div>

        <?php if ($schedule['notes']): ?>
        <div class="notes-section">
            <h4 class="notes-title">📝 ملاحظات:</h4>
            <div class="notes-content"><?= nl2br(htmlspecialchars($schedule['notes'])) ?></div>
        </div>
        <?php endif; ?>

        <div class="print-footer">
            <p>تم إنشاء هذا الجدول بواسطة نظام إدارة حراسة الامتحانات</p>
            <p>آخر تحديث: <?= date('Y-m-d H:i:s', strtotime($schedule['updated_at'])) ?></p>
        </div>
    </div>

    <script>
        // Print functionality
        function printSchedule() {
            window.print();
        }

        // Add keyboard shortcut for printing (Ctrl+P)
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                printSchedule();
            }
        });

        // Show print preview message
        window.addEventListener('beforeprint', function() {
            console.log('جاري تحضير الطباعة...');
        });

        window.addEventListener('afterprint', function() {
            console.log('تم الانتهاء من الطباعة');
        });

        // Auto-focus on print button when page loads
        window.onload = function() {
            const printBtn = document.querySelector('button[onclick="window.print()"]');
            if (printBtn) {
                printBtn.focus();
            }
        };
    </script>
</body>
</html>
