<?php
/**
 * صفحة استيراد الحراس من Excel
 */

require_once '../../config/database.php';

// مكتبة SimpleXLSX المدمجة لقراءة Excel
class SimpleXLSX {
    public static function parse($file) {
        if (!file_exists($file)) {
            return false;
        }

        $zip = new ZipArchive();
        if ($zip->open($file) !== TRUE) {
            return false;
        }

        // قراءة ملف الشيت الأول
        $sharedStrings = [];
        $worksheetData = '';

        // قراءة النصوص المشتركة
        if (($sharedStringsXML = $zip->getFromName('xl/sharedStrings.xml')) !== false) {
            $xml = simplexml_load_string($sharedStringsXML);
            foreach ($xml->si as $si) {
                $sharedStrings[] = (string)$si->t;
            }
        }

        // قراءة بيانات الشيت
        if (($worksheetXML = $zip->getFromName('xl/worksheets/sheet1.xml')) !== false) {
            $xml = simplexml_load_string($worksheetXML);
            $rows = [];

            foreach ($xml->sheetData->row as $row) {
                $rowData = [];
                foreach ($row->c as $cell) {
                    $value = '';
                    if (isset($cell->v)) {
                        if (isset($cell['t']) && $cell['t'] == 's') {
                            // نص مشترك
                            $value = $sharedStrings[(int)$cell->v];
                        } else {
                            // قيمة مباشرة
                            $value = (string)$cell->v;
                        }
                    }
                    $rowData[] = $value;
                }
                $rows[] = $rowData;
            }

            $zip->close();
            return $rows;
        }

        $zip->close();
        return false;
    }
}

$database = new Database();
$conn = $database->getConnection();

$message = '';
$messageType = '';
$importResults = null;

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_FILES['excel_file'])) {
    $uploadedFile = $_FILES['excel_file'];

    // التحقق من نوع الملف
    $allowedTypes = [
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/octet-stream'
    ];
    $fileExtension = strtolower(pathinfo($uploadedFile['name'], PATHINFO_EXTENSION));

    if (!in_array($uploadedFile['type'], $allowedTypes) && !in_array($fileExtension, ['xls', 'xlsx'])) {
        $message = 'يرجى رفع ملف Excel صحيح (.xls أو .xlsx)';
        $messageType = 'error';
    } else {
        try {
            $rows = [];

            if ($fileExtension === 'xlsx') {
                // قراءة ملف XLSX
                $rows = SimpleXLSX::parse($uploadedFile['tmp_name']);
                if ($rows === false) {
                    throw new Exception('فشل في قراءة ملف Excel');
                }
            } elseif ($fileExtension === 'xls') {
                // قراءة ملف XLS القديم - تحويل إلى CSV أولاً
                $message = 'يرجى حفظ الملف بصيغة .xlsx أو تحويله إلى CSV';
                $messageType = 'error';
            } else {
                throw new Exception('نوع ملف غير مدعوم');
            }

            if (count($rows) < 2) {
                throw new Exception('الملف يجب أن يحتوي على هيدر وصف واحد على الأقل');
            }

            // الصف الأول هو الهيدر
            $headers = array_map('trim', $rows[0]);
            $dataRows = array_slice($rows, 1);

            // البحث عن الأعمدة المطلوبة
            $columnMap = [];
            $possibleColumns = [
                'guard_number' => ['الرقم', 'رقم', 'رقم الحارس', 'ID', 'id', 'Number'],
                'first_name' => ['الإسم', 'الاسم', 'اسم', 'الإسم الأول', 'First Name'],
                'last_name' => ['اللقب', 'لقب', 'الإسم الأخير', 'Last Name'],
                'institution' => ['مؤسسة العمل', 'المؤسسة', 'مؤسسة', 'Institution'],
                'subject' => ['المادة', 'مادة', 'Subject'],
                'phone' => ['الهاتف', 'رقم الهاتف', 'Phone'],
                'email' => ['البريد', 'البريد الإلكتروني', 'Email']
            ];

            foreach ($possibleColumns as $field => $possibilities) {
                foreach ($headers as $index => $header) {
                    foreach ($possibilities as $possible) {
                        if (stripos($header, $possible) !== false) {
                            $columnMap[$field] = $index;
                            break 2;
                        }
                    }
                }
            }

            // التحقق من الأعمدة الأساسية
            $requiredColumns = ['guard_number', 'first_name', 'last_name', 'institution'];
            $missingColumns = array_filter($requiredColumns, function($col) use ($columnMap) {
                return !isset($columnMap[$col]);
            });

            if (!empty($missingColumns)) {
                throw new Exception('الأعمدة التالية مفقودة: ' . implode(', ', $missingColumns));
            }

            // معالجة البيانات
            $successCount = 0;
            $errorCount = 0;
            $errors = [];

            $conn->beginTransaction();

            foreach ($dataRows as $rowIndex => $row) {
                // تجاهل الصفوف الفارغة
                if (empty(array_filter($row))) {
                    continue;
                }

                $guardData = [
                    'guard_number' => isset($columnMap['guard_number']) ? trim($row[$columnMap['guard_number']]) : '',
                    'first_name' => isset($columnMap['first_name']) ? trim($row[$columnMap['first_name']]) : '',
                    'last_name' => isset($columnMap['last_name']) ? trim($row[$columnMap['last_name']]) : '',
                    'institution' => isset($columnMap['institution']) ? trim($row[$columnMap['institution']]) : '',
                    'subject' => isset($columnMap['subject']) ? trim($row[$columnMap['subject']]) : 'غير محدد',
                    'phone' => isset($columnMap['phone']) ? trim($row[$columnMap['phone']]) : null,
                    'email' => isset($columnMap['email']) ? trim($row[$columnMap['email']]) : null
                ];

                // التحقق من صحة البيانات الأساسية
                if (empty($guardData['guard_number']) || empty($guardData['first_name']) || empty($guardData['last_name']) || empty($guardData['institution'])) {
                    $errors[] = "الصف " . ($rowIndex + 2) . ": بيانات ناقصة";
                    $errorCount++;
                    continue;
                }

                try {
                    // التحقق من عدم وجود رقم الحارس مسبقاً
                    $checkStmt = $conn->prepare("SELECT id FROM guards WHERE guard_number = ?");
                    $checkStmt->execute([$guardData['guard_number']]);

                    if ($checkStmt->fetch()) {
                        // تحديث البيانات الموجودة
                        $updateStmt = $conn->prepare("
                            UPDATE guards
                            SET first_name = ?, last_name = ?, institution = ?, subject = ?, phone = ?, email = ?, is_active = 1, updated_at = NOW()
                            WHERE guard_number = ?
                        ");
                        $updateStmt->execute([
                            $guardData['first_name'],
                            $guardData['last_name'],
                            $guardData['institution'],
                            $guardData['subject'],
                            $guardData['phone'],
                            $guardData['email'],
                            $guardData['guard_number']
                        ]);
                    } else {
                        // إدراج حارس جديد
                        $insertStmt = $conn->prepare("
                            INSERT INTO guards (guard_number, first_name, last_name, institution, subject, phone, email)
                            VALUES (?, ?, ?, ?, ?, ?, ?)
                        ");
                        $insertStmt->execute([
                            $guardData['guard_number'],
                            $guardData['first_name'],
                            $guardData['last_name'],
                            $guardData['institution'],
                            $guardData['subject'],
                            $guardData['phone'],
                            $guardData['email']
                        ]);
                    }

                    $successCount++;

                } catch (PDOException $e) {
                    $errors[] = "الصف " . ($rowIndex + 2) . ": " . $e->getMessage();
                    $errorCount++;
                }
            }

            $conn->commit();

            $importResults = [
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'errors' => $errors,
                'total_rows' => count($dataRows)
            ];

            if ($successCount > 0) {
                $message = "تم استيراد {$successCount} حارس بنجاح";
                $messageType = 'success';
                if ($errorCount > 0) {
                    $message .= " مع {$errorCount} أخطاء";
                }
            } else {
                $message = "لم يتم استيراد أي حارس";
                $messageType = 'error';
            }

        } catch (Exception $e) {
            $conn->rollBack();
            $message = 'خطأ في قراءة الملف: ' . $e->getMessage();
            $messageType = 'error';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استيراد الحراس من Excel - نظام إدارة حراسة الامتحانات</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .upload-area {
            border: 3px dashed var(--border-color);
            border-radius: var(--border-radius);
            padding: 40px;
            text-align: center;
            transition: var(--transition);
            cursor: pointer;
            background: var(--light-color);
            margin-bottom: 30px;
        }

        .upload-area:hover {
            border-color: var(--primary-color);
            background: #f0f9ff;
        }

        .upload-area.dragover {
            border-color: var(--success-color);
            background: #f0fdf4;
        }

        .upload-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            color: var(--primary-color);
        }

        .upload-text {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--dark-color);
        }

        .upload-hint {
            color: #666;
            font-size: 0.9rem;
        }

        .file-input {
            display: none;
        }

        .requirements {
            background: #fef3c7;
            border: 1px solid var(--accent-color);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 30px;
        }

        .requirements h3 {
            color: #92400e;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .requirements ul {
            list-style: none;
            padding: 0;
        }

        .requirements li {
            color: #92400e;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .requirements li::before {
            content: "✓";
            color: var(--success-color);
            font-weight: bold;
        }

        .results-section {
            background: white;
            border-radius: var(--border-radius);
            padding: 25px;
            margin-top: 30px;
            box-shadow: var(--shadow-light);
        }

        .results-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .stat-item.success {
            background: #d1fae5;
            border-color: var(--success-color);
        }

        .stat-item.error {
            background: #fee2e2;
            border-color: var(--danger-color);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }

        .errors-list {
            background: #fee2e2;
            border: 1px solid var(--danger-color);
            border-radius: 8px;
            padding: 15px;
            max-height: 200px;
            overflow-y: auto;
        }

        .errors-list h4 {
            color: var(--danger-color);
            margin-bottom: 10px;
        }

        .errors-list ul {
            list-style: none;
            padding: 0;
        }

        .errors-list li {
            color: #991b1b;
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📁 استيراد الحراس من Excel</h1>
            <p>رفع وتحليل ملف Excel لإضافة الحراس بشكل جماعي</p>
            <a href="index.php" class="btn btn-secondary">← العودة لقائمة الحراس</a>
        </div>

        <?php if ($message): ?>
        <div class="alert alert-<?= $messageType ?>">
            <?= htmlspecialchars($message) ?>
        </div>
        <?php endif; ?>

        <div class="requirements">
            <h3>⚠️ متطلبات الملف:</h3>
            <ul>
                <li>يجب أن يحتوي الصف الأول على عناوين الأعمدة</li>
                <li>عمود "الرقم" أو "رقم الحارس" - رقم الحارس (مطلوب)</li>
                <li>عمود "الإسم" أو "الاسم الأول" - الاسم الأول (مطلوب)</li>
                <li>عمود "اللقب" أو "الاسم الأخير" - اسم العائلة (مطلوب)</li>
                <li>عمود "مؤسسة العمل" أو "المؤسسة" - مكان العمل (مطلوب)</li>
                <li>عمود "المادة" - التخصص (اختياري)</li>
                <li>عمود "الهاتف" أو "رقم الهاتف" - رقم الهاتف (اختياري)</li>
                <li>عمود "البريد الإلكتروني" أو "Email" - البريد الإلكتروني (اختياري)</li>
            </ul>
        </div>

        <form method="POST" enctype="multipart/form-data">
            <div class="upload-area" onclick="document.getElementById('excel-file').click()"
                 ondrop="handleDrop(event)" ondragover="handleDragOver(event)" ondragleave="handleDragLeave(event)">
                <div class="upload-icon">📁</div>
                <div class="upload-text">انقر هنا أو اسحب ملف Excel</div>
                <div class="upload-hint">يدعم ملفات .xlsx و .xls</div>
            </div>

            <input type="file" id="excel-file" name="excel_file" class="file-input" accept=".xlsx,.xls" onchange="handleFileSelect(this)">

            <div style="text-align: center;">
                <button type="submit" class="btn btn-success btn-lg" id="upload-btn" disabled>
                    📊 تحليل واستيراد الملف
                </button>
                <a href="sample.php" class="btn btn-info btn-lg" style="margin-right: 15px;">
                    📋 تحميل نموذج الملف
                </a>
            </div>
        </form>

        <?php if ($importResults): ?>
        <div class="results-section">
            <h3>📊 نتائج الاستيراد</h3>

            <div class="results-stats">
                <div class="stat-item success">
                    <div class="stat-number"><?= $importResults['success_count'] ?></div>
                    <div class="stat-label">تم بنجاح</div>
                </div>
                <div class="stat-item error">
                    <div class="stat-number"><?= $importResults['error_count'] ?></div>
                    <div class="stat-label">أخطاء</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= $importResults['total_rows'] ?></div>
                    <div class="stat-label">إجمالي الصفوف</div>
                </div>
            </div>

            <?php if (!empty($importResults['errors'])): ?>
            <div class="errors-list">
                <h4>تفاصيل الأخطاء:</h4>
                <ul>
                    <?php foreach ($importResults['errors'] as $error): ?>
                    <li><?= htmlspecialchars($error) ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endif; ?>

            <div style="text-align: center; margin-top: 20px;">
                <a href="index.php" class="btn btn-primary">عرض قائمة الحراس</a>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script>
        function handleFileSelect(input) {
            const uploadBtn = document.getElementById('upload-btn');
            const uploadArea = document.querySelector('.upload-area');

            if (input.files.length > 0) {
                uploadBtn.disabled = false;
                uploadArea.querySelector('.upload-text').textContent = 'تم اختيار الملف: ' + input.files[0].name;
            } else {
                uploadBtn.disabled = true;
                uploadArea.querySelector('.upload-text').textContent = 'انقر هنا أو اسحب ملف Excel';
            }
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.currentTarget.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('excel-file').files = files;
                handleFileSelect(document.getElementById('excel-file'));
            }
        }
    </script>
</body>
</html>
