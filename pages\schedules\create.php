<?php
/**
 * صفحة إنشاء جدول حراسة جديد
 */

require_once '../../config/database.php';

$database = new Database();
$conn = $database->getConnection();

$message = '';
$messageType = '';
$currentStep = 1;
$scheduleId = null;

// جلب إعدادات المركز
$stmt = $conn->prepare("SELECT * FROM center_settings WHERE id = 1");
$stmt->execute();
$centerSettings = $stmt->fetch();

// جلب قائمة الحراس النشطين
$stmt = $conn->prepare("SELECT * FROM guards WHERE is_active = 1 ORDER BY guard_number ASC");
$stmt->execute();
$guards = $stmt->fetchAll();

// معالجة الخطوات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['step'])) {
        $currentStep = intval($_POST['step']);

        switch ($currentStep) {
            case 1:
                // إنشاء جدول الحراسة الأساسي
                try {
                    $stmt = $conn->prepare("
                        INSERT INTO guard_schedules (schedule_name, exam_date, exam_period, exam_type, status, created_by)
                        VALUES (?, ?, ?, ?, 'draft', ?)
                    ");
                    $stmt->execute([
                        $_POST['schedule_name'],
                        $_POST['exam_date'],
                        $_POST['exam_period'],
                        $_POST['exam_type'],
                        'النظام'
                    ]);

                    $scheduleId = $conn->lastInsertId();
                    $currentStep = 2;
                    $message = 'تم إنشاء الجدول بنجاح، يمكنك الآن تعيين الحراس';
                    $messageType = 'success';

                } catch(PDOException $e) {
                    $message = 'خطأ في إنشاء الجدول: ' . $e->getMessage();
                    $messageType = 'error';
                }
                break;

            case 2:
                // حفظ تعيينات الحراس
                $scheduleId = $_POST['schedule_id'];

                try {
                    $conn->beginTransaction();

                    // حذف التعيينات السابقة
                    $stmt = $conn->prepare("DELETE FROM guard_assignments WHERE schedule_id = ?");
                    $stmt->execute([$scheduleId]);

                    // إضافة التعيينات الجديدة
                    $stmt = $conn->prepare("
                        INSERT INTO guard_assignments (schedule_id, guard_id, room_number, guard_type, guard_order, status)
                        VALUES (?, ?, ?, ?, ?, 'present')
                    ");

                    $roomsCount = $centerSettings['rooms_count'];

                    for ($room = 1; $room <= $roomsCount; $room++) {
                        $roomNumber = str_pad($room, 2, '0', STR_PAD_LEFT);

                        // الحارس الرئيسي
                        if (!empty($_POST["main_guard_$room"])) {
                            $stmt->execute([$scheduleId, $_POST["main_guard_$room"], $roomNumber, 'main', 1]);
                        }

                        // الحراس العاديين
                        if (!empty($_POST["guard2_$room"])) {
                            $stmt->execute([$scheduleId, $_POST["guard2_$room"], $roomNumber, 'regular', 2]);
                        }

                        if (!empty($_POST["guard3_$room"])) {
                            $stmt->execute([$scheduleId, $_POST["guard3_$room"], $roomNumber, 'regular', 3]);
                        }
                    }

                    // تحديث حالة الجدول
                    $stmt = $conn->prepare("UPDATE guard_schedules SET status = 'completed' WHERE id = ?");
                    $stmt->execute([$scheduleId]);

                    $conn->commit();

                    $message = 'تم حفظ جدول الحراسة بنجاح!';
                    $messageType = 'success';
                    $currentStep = 3;

                } catch(PDOException $e) {
                    $conn->rollBack();
                    $message = 'خطأ في حفظ التعيينات: ' . $e->getMessage();
                    $messageType = 'error';
                }
                break;
        }
    }
}

// إذا كان هناك معرف جدول في URL
if (isset($_GET['id'])) {
    $scheduleId = $_GET['id'];
    $currentStep = 2;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء جدول حراسة - نظام إدارة حراسة الامتحانات</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .steps-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            gap: 20px;
        }

        .step {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px 20px;
            border-radius: 25px;
            background: var(--light-color);
            border: 2px solid var(--border-color);
            color: #666;
            font-weight: 600;
        }

        .step.active {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .step.completed {
            background: var(--success-color);
            border-color: var(--success-color);
            color: white;
        }

        .step-number {
            width: 25px;
            height: 25px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            font-weight: 700;
        }

        .rooms-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .room-card {
            background: white;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 20px;
            box-shadow: var(--shadow-light);
        }

        .room-header {
            text-align: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .room-number {
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .guard-selector {
            margin-bottom: 15px;
        }

        .guard-selector label {
            display: block;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--dark-color);
            font-size: 0.9rem;
        }

        .guard-select {
            width: 100%;
            padding: 10px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-family: 'Cairo', sans-serif;
            font-size: 0.9rem;
        }

        .guard-select.main-guard {
            border-color: var(--danger-color);
            background: #fef2f2;
        }

        .selected-guards-display {
            margin-top: 15px;
            padding: 15px;
            background: #f8fafc;
            border-radius: var(--border-radius);
            border: 2px dashed var(--border-color);
            min-height: 80px;
        }

        .selected-guards-display.has-guards {
            border-color: var(--success-color);
            background: #f0fdf4;
        }

        .selected-guard-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: white;
            padding: 8px 12px;
            margin-bottom: 8px;
            border-radius: 6px;
            border: 1px solid var(--border-color);
        }

        .selected-guard-info {
            font-weight: 600;
            color: var(--dark-color);
            font-size: 0.85rem;
        }

        .selected-guard-rank {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
        }

        .selected-guard-rank.main {
            background: var(--danger-color);
            color: white;
        }

        .selected-guard-rank.regular {
            background: var(--primary-color);
            color: white;
        }

        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            gap: 15px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📅 إنشاء جدول حراسة جديد</h1>
            <p>إنشاء وتنظيم جدول حراسة للامتحانات</p>
            <a href="../../index.php" class="btn btn-secondary">← العودة للرئيسية</a>
        </div>

        <?php if ($message): ?>
        <div class="alert alert-<?= $messageType ?>">
            <?= htmlspecialchars($message) ?>
        </div>
        <?php endif; ?>

        <div class="steps-indicator">
            <div class="step <?= $currentStep >= 1 ? 'active' : '' ?> <?= $currentStep > 1 ? 'completed' : '' ?>">
                <div class="step-number">1</div>
                <span>معلومات الجدول</span>
            </div>
            <div class="step <?= $currentStep >= 2 ? 'active' : '' ?> <?= $currentStep > 2 ? 'completed' : '' ?>">
                <div class="step-number">2</div>
                <span>تعيين الحراس</span>
            </div>
            <div class="step <?= $currentStep >= 3 ? 'active' : '' ?>">
                <div class="step-number">3</div>
                <span>المراجعة النهائية</span>
            </div>
        </div>

        <?php if ($currentStep == 1): ?>
        <!-- الخطوة الأولى: معلومات الجدول -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">الخطوة 1: معلومات الجدول الأساسية</h3>
                <p class="card-subtitle">أدخل المعلومات الأساسية لجدول الحراسة</p>
            </div>

            <form method="POST">
                <input type="hidden" name="step" value="1">

                <div class="form-row">
                    <div class="form-group">
                        <label>اسم الجدول *</label>
                        <input type="text" name="schedule_name" class="form-control" required
                               placeholder="مثال: جدول حراسة امتحان شهادة التعليم المتوسط">
                    </div>

                    <div class="form-group">
                        <label>تاريخ الامتحان *</label>
                        <input type="date" name="exam_date" class="form-control" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>فترة الامتحان *</label>
                        <select name="exam_period" class="form-control" required>
                            <option value="">-- اختر الفترة --</option>
                            <option value="morning">الفترة الصباحية</option>
                            <option value="evening">الفترة المسائية</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>نوع الامتحان *</label>
                        <select name="exam_type" class="form-control" required>
                            <option value="">-- اختر نوع الامتحان --</option>
                            <option value="bem" <?= $centerSettings && $centerSettings['exam_type'] == 'bem' ? 'selected' : '' ?>>
                                شهادة التعليم المتوسط
                            </option>
                            <option value="bac" <?= $centerSettings && $centerSettings['exam_type'] == 'bac' ? 'selected' : '' ?>>
                                شهادة البكالوريا
                            </option>
                        </select>
                    </div>
                </div>

                <div class="navigation-buttons">
                    <div></div>
                    <button type="submit" class="btn btn-primary btn-lg">التالي ← تعيين الحراس</button>
                </div>
            </form>
        </div>

        <?php elseif ($currentStep == 2): ?>
        <!-- الخطوة الثانية: تعيين الحراس -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">الخطوة 2: تعيين الحراس للقاعات</h3>
                <p class="card-subtitle">اختر الحراس لكل قاعة (حارس رئيسي + حراسين عاديين)</p>
            </div>

            <form method="POST" id="assignmentForm">
                <input type="hidden" name="step" value="2">
                <input type="hidden" name="schedule_id" value="<?= $scheduleId ?>">

                <div class="rooms-grid">
                    <?php for ($room = 1; $room <= ($centerSettings['rooms_count'] ?? 14); $room++): ?>
                    <div class="room-card">
                        <div class="room-header">
                            <div class="room-number">قاعة <?= str_pad($room, 2, '0', STR_PAD_LEFT) ?></div>
                        </div>

                        <div class="guard-selector">
                            <label>الحارس الرئيسي:</label>
                            <select name="main_guard_<?= $room ?>" class="guard-select main-guard" data-room="<?= $room ?>" data-type="main">
                                <option value="">-- اختر حارس رئيسي --</option>
                                <?php foreach ($guards as $guard): ?>
                                <option value="<?= $guard['id'] ?>">
                                    <?= htmlspecialchars($guard['guard_number'] . ' - ' . $guard['first_name'] . ' ' . $guard['last_name']) ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="guard-selector">
                            <label>الحارس العادي الأول:</label>
                            <select name="guard2_<?= $room ?>" class="guard-select" data-room="<?= $room ?>" data-type="regular">
                                <option value="">-- اختر حارس --</option>
                                <?php foreach ($guards as $guard): ?>
                                <option value="<?= $guard['id'] ?>">
                                    <?= htmlspecialchars($guard['guard_number'] . ' - ' . $guard['first_name'] . ' ' . $guard['last_name']) ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="guard-selector">
                            <label>الحارس العادي الثاني:</label>
                            <select name="guard3_<?= $room ?>" class="guard-select" data-room="<?= $room ?>" data-type="regular">
                                <option value="">-- اختر حارس --</option>
                                <?php foreach ($guards as $guard): ?>
                                <option value="<?= $guard['id'] ?>">
                                    <?= htmlspecialchars($guard['guard_number'] . ' - ' . $guard['first_name'] . ' ' . $guard['last_name']) ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="selected-guards-display" id="display-<?= $room ?>">
                            <div style="text-align: center; color: #999; font-style: italic; font-size: 0.8rem; padding: 20px 10px;">
                                لم يتم تعيين حراس بعد
                            </div>
                        </div>
                    </div>
                    <?php endfor; ?>
                </div>

                <div class="navigation-buttons">
                    <a href="?" class="btn btn-secondary">← السابق</a>
                    <button type="submit" class="btn btn-success btn-lg">حفظ الجدول ←</button>
                </div>
            </form>
        </div>

        <?php elseif ($currentStep == 3): ?>
        <!-- الخطوة الثالثة: المراجعة النهائية -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">✅ تم إنشاء جدول الحراسة بنجاح!</h3>
                <p class="card-subtitle">يمكنك الآن عرض الجدول أو طباعته أو العودة لإنشاء جدول جديد</p>
            </div>

            <div style="text-align: center; padding: 40px;">
                <div style="font-size: 4rem; margin-bottom: 20px;">🎉</div>
                <h2 style="color: var(--success-color); margin-bottom: 20px;">تم الحفظ بنجاح!</h2>
                <p style="color: #666; margin-bottom: 30px;">تم إنشاء وحفظ جدول الحراسة بنجاح في قاعدة البيانات</p>

                <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                    <a href="view.php?id=<?= $scheduleId ?>" class="btn btn-primary btn-lg">👁️ عرض الجدول</a>
                    <a href="print.php?id=<?= $scheduleId ?>" class="btn btn-info btn-lg" target="_blank">🖨️ طباعة الجدول</a>
                    <a href="index.php" class="btn btn-secondary btn-lg">📋 عرض جميع الجداول</a>
                    <a href="create.php" class="btn btn-success btn-lg">➕ إنشاء جدول جديد</a>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <script>
        // تحديث عرض الحراس المختارين
        function updateGuardsDisplay(roomNumber) {
            const displayElement = document.getElementById(`display-${roomNumber}`);
            const mainGuardSelect = document.querySelector(`select[name="main_guard_${roomNumber}"]`);
            const guard2Select = document.querySelector(`select[name="guard2_${roomNumber}"]`);
            const guard3Select = document.querySelector(`select[name="guard3_${roomNumber}"]`);

            let html = '';
            let hasGuards = false;

            // الحارس الرئيسي
            if (mainGuardSelect.value) {
                const selectedOption = mainGuardSelect.options[mainGuardSelect.selectedIndex];
                html += `
                    <div class="selected-guard-item">
                        <span class="selected-guard-info">${selectedOption.text}</span>
                        <span class="selected-guard-rank main">حارس رئيسي</span>
                    </div>
                `;
                hasGuards = true;
            }

            // الحارس العادي الأول
            if (guard2Select.value) {
                const selectedOption = guard2Select.options[guard2Select.selectedIndex];
                html += `
                    <div class="selected-guard-item">
                        <span class="selected-guard-info">${selectedOption.text}</span>
                        <span class="selected-guard-rank regular">حارس عادي 1</span>
                    </div>
                `;
                hasGuards = true;
            }

            // الحارس العادي الثاني
            if (guard3Select.value) {
                const selectedOption = guard3Select.options[guard3Select.selectedIndex];
                html += `
                    <div class="selected-guard-item">
                        <span class="selected-guard-info">${selectedOption.text}</span>
                        <span class="selected-guard-rank regular">حارس عادي 2</span>
                    </div>
                `;
                hasGuards = true;
            }

            if (!hasGuards) {
                html = '<div style="text-align: center; color: #999; font-style: italic; font-size: 0.8rem; padding: 20px 10px;">لم يتم تعيين حراس بعد</div>';
            }

            displayElement.innerHTML = html;

            if (hasGuards) {
                displayElement.classList.add('has-guards');
            } else {
                displayElement.classList.remove('has-guards');
            }
        }

        // إضافة مستمعي الأحداث لجميع القوائم
        document.addEventListener('DOMContentLoaded', function() {
            const selects = document.querySelectorAll('.guard-select');
            selects.forEach(select => {
                select.addEventListener('change', function() {
                    const room = this.dataset.room;
                    updateGuardsDisplay(room);
                    updateAvailableGuards();
                });
            });
        });

        // تحديث الحراس المتاحين (منع التكرار)
        function updateAvailableGuards() {
            const allSelects = document.querySelectorAll('.guard-select');
            const selectedGuards = new Set();

            // جمع جميع الحراس المختارين
            allSelects.forEach(select => {
                if (select.value) {
                    selectedGuards.add(select.value);
                }
            });

            // تحديث كل قائمة
            allSelects.forEach(select => {
                const currentValue = select.value;
                const options = select.querySelectorAll('option');

                options.forEach(option => {
                    if (option.value && option.value !== currentValue && selectedGuards.has(option.value)) {
                        option.style.display = 'none';
                    } else {
                        option.style.display = 'block';
                    }
                });
            });
        }
    </script>
</body>
</html>
