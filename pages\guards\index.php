<?php
/**
 * صفحة إدارة الحراس
 */

require_once '../../config/database.php';

$database = new Database();
$conn = $database->getConnection();

// معالجة العمليات
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_guard':
                try {
                    $stmt = $conn->prepare("INSERT INTO guards (guard_number, first_name, last_name, institution, subject, phone, email) VALUES (?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([
                        $_POST['guard_number'],
                        $_POST['first_name'],
                        $_POST['last_name'],
                        $_POST['institution'],
                        $_POST['subject'] ?: 'غير محدد',
                        $_POST['phone'] ?: null,
                        $_POST['email'] ?: null
                    ]);
                    $message = 'تم إضافة الحارس بنجاح';
                    $messageType = 'success';
                } catch(PDOException $e) {
                    $message = 'خطأ في إضافة الحارس: ' . $e->getMessage();
                    $messageType = 'error';
                }
                break;

            case 'delete_guard':
                try {
                    $stmt = $conn->prepare("UPDATE guards SET is_active = 0 WHERE id = ?");
                    $stmt->execute([$_POST['guard_id']]);
                    $message = 'تم حذف الحارس بنجاح';
                    $messageType = 'success';
                } catch(PDOException $e) {
                    $message = 'خطأ في حذف الحارس: ' . $e->getMessage();
                    $messageType = 'error';
                }
                break;
        }
    }
}

// جلب قائمة الحراس
$search = $_GET['search'] ?? '';
$institution_filter = $_GET['institution'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

$where_conditions = ["is_active = 1"];
$params = [];

if ($search) {
    $where_conditions[] = "(guard_number LIKE ? OR first_name LIKE ? OR last_name LIKE ? OR CONCAT(first_name, ' ', last_name) LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
}

if ($institution_filter) {
    $where_conditions[] = "institution = ?";
    $params[] = $institution_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// عدد الحراس الإجمالي
$count_stmt = $conn->prepare("SELECT COUNT(*) as total FROM guards WHERE $where_clause");
$count_stmt->execute($params);
$total_guards = $count_stmt->fetch()['total'];
$total_pages = ceil($total_guards / $limit);

// جلب الحراس
$stmt = $conn->prepare("SELECT * FROM guards WHERE $where_clause ORDER BY guard_number ASC LIMIT $limit OFFSET $offset");
$stmt->execute($params);
$guards = $stmt->fetchAll();

// جلب قائمة المؤسسات للفلترة
$institutions_stmt = $conn->prepare("SELECT DISTINCT institution FROM guards WHERE is_active = 1 ORDER BY institution");
$institutions_stmt->execute();
$institutions = $institutions_stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الحراس - نظام إدارة حراسة الامتحانات</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .guards-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .search-filters {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }

        .search-input, .filter-select {
            padding: 10px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
        }

        .search-input {
            min-width: 250px;
        }

        .guards-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .guards-table th,
        .guards-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid var(--border-color);
        }

        .guards-table th {
            background: var(--light-color);
            font-weight: 600;
            color: var(--dark-color);
        }

        .guards-table tr:hover {
            background: #f8fafc;
        }

        .guard-number {
            font-weight: 700;
            color: var(--primary-color);
        }

        .guard-name {
            font-weight: 600;
        }

        .institution {
            font-size: 0.9rem;
            color: #666;
        }

        .actions {
            display: flex;
            gap: 8px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.85rem;
        }

        .btn-danger {
            background: var(--danger-color);
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination a,
        .pagination span {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            text-decoration: none;
            color: var(--dark-color);
        }

        .pagination .current {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .alert-success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }

        .alert-error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👥 إدارة الحراس</h1>
            <p>إضافة وتعديل وإدارة قائمة الحراس</p>
            <a href="../../index.php" class="btn btn-secondary">← العودة للرئيسية</a>
        </div>

        <?php if ($message): ?>
        <div class="alert alert-<?= $messageType ?>">
            <?= htmlspecialchars($message) ?>
        </div>
        <?php endif; ?>

        <div class="guards-header">
            <div class="search-filters">
                <form method="GET" style="display: flex; gap: 15px; align-items: center;">
                    <input type="text" name="search" placeholder="البحث بالرقم أو الاسم..."
                           value="<?= htmlspecialchars($search) ?>" class="search-input">

                    <select name="institution" class="filter-select">
                        <option value="">جميع المؤسسات</option>
                        <?php foreach ($institutions as $inst): ?>
                        <option value="<?= htmlspecialchars($inst['institution']) ?>"
                                <?= $institution_filter == $inst['institution'] ? 'selected' : '' ?>>
                            <?= htmlspecialchars($inst['institution']) ?>
                        </option>
                        <?php endforeach; ?>
                    </select>

                    <button type="submit" class="btn btn-primary">بحث</button>
                    <a href="?" class="btn btn-secondary">مسح</a>
                </form>
            </div>

            <div>
                <button onclick="openAddModal()" class="btn btn-success">+ إضافة حارس جديد</button>
                <a href="import.php" class="btn btn-info">📁 استيراد Excel</a>
            </div>
        </div>

        <div style="margin-bottom: 15px; color: #666;">
            إجمالي الحراس: <strong><?= $total_guards ?></strong>
        </div>

        <table class="guards-table">
            <thead>
                <tr>
                    <th>رقم الحارس</th>
                    <th>الاسم الكامل</th>
                    <th>مؤسسة العمل</th>
                    <th>المادة</th>
                    <th>رقم الهاتف</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($guards)): ?>
                <tr>
                    <td colspan="6" style="text-align: center; padding: 40px; color: #666;">
                        لا توجد حراس مسجلين
                    </td>
                </tr>
                <?php else: ?>
                <?php foreach ($guards as $guard): ?>
                <tr>
                    <td class="guard-number"><?= htmlspecialchars($guard['guard_number']) ?></td>
                    <td class="guard-name">
                        <?= htmlspecialchars($guard['first_name'] . ' ' . $guard['last_name']) ?>
                    </td>
                    <td class="institution"><?= htmlspecialchars($guard['institution']) ?></td>
                    <td><?= htmlspecialchars($guard['subject']) ?></td>
                    <td><?= htmlspecialchars($guard['phone'] ?: '-') ?></td>
                    <td class="actions">
                        <button onclick="editGuard(<?= $guard['id'] ?>)" class="btn btn-sm btn-primary">تعديل</button>
                        <button onclick="deleteGuard(<?= $guard['id'] ?>)" class="btn btn-sm btn-danger">حذف</button>
                    </td>
                </tr>
                <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>

        <?php if ($total_pages > 1): ?>
        <div class="pagination">
            <?php if ($page > 1): ?>
            <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&institution=<?= urlencode($institution_filter) ?>">السابق</a>
            <?php endif; ?>

            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
            <?php if ($i == $page): ?>
            <span class="current"><?= $i ?></span>
            <?php else: ?>
            <a href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&institution=<?= urlencode($institution_filter) ?>"><?= $i ?></a>
            <?php endif; ?>
            <?php endfor; ?>

            <?php if ($page < $total_pages): ?>
            <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&institution=<?= urlencode($institution_filter) ?>">التالي</a>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </div>

    <!-- نافذة إضافة حارس -->
    <div id="addModal" class="modal">
        <div class="modal-content">
            <h3>إضافة حارس جديد</h3>
            <form method="POST">
                <input type="hidden" name="action" value="add_guard">

                <div class="form-group">
                    <label>رقم الحارس *</label>
                    <input type="text" name="guard_number" required>
                </div>

                <div class="form-group">
                    <label>الاسم الأول *</label>
                    <input type="text" name="first_name" required>
                </div>

                <div class="form-group">
                    <label>اللقب *</label>
                    <input type="text" name="last_name" required>
                </div>

                <div class="form-group">
                    <label>مؤسسة العمل *</label>
                    <input type="text" name="institution" required>
                </div>

                <div class="form-group">
                    <label>المادة</label>
                    <input type="text" name="subject" placeholder="اختياري">
                </div>

                <div class="form-group">
                    <label>رقم الهاتف</label>
                    <input type="tel" name="phone" placeholder="اختياري">
                </div>

                <div class="form-group">
                    <label>البريد الإلكتروني</label>
                    <input type="email" name="email" placeholder="اختياري">
                </div>

                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button type="button" onclick="closeModal()" class="btn btn-secondary">إلغاء</button>
                    <button type="submit" class="btn btn-success">إضافة</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function openAddModal() {
            document.getElementById('addModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('addModal').style.display = 'none';
        }

        function deleteGuard(id) {
            if (confirm('هل أنت متأكد من حذف هذا الحارس؟')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = `
                    <input type="hidden" name="action" value="delete_guard">
                    <input type="hidden" name="guard_id" value="${id}">
                `;
                document.body.appendChild(form);
                form.submit();
            }
        }

        // إغلاق النافذة عند النقر خارجها
        window.onclick = function(event) {
            const modal = document.getElementById('addModal');
            if (event.target == modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>
