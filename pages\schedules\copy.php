<?php
/**
 * صفحة نسخ جدول الحراسة
 */

require_once '../../config/database.php';

$database = new Database();
$conn = $database->getConnection();

$originalId = $_GET['id'] ?? null;

if (!$originalId) {
    header('Location: index.php');
    exit();
}

// جلب بيانات الجدول الأصلي
try {
    $stmt = $conn->prepare("SELECT * FROM guard_schedules WHERE id = ?");
    $stmt->execute([$originalId]);
    $originalSchedule = $stmt->fetch();
    
    if (!$originalSchedule) {
        header('Location: index.php');
        exit();
    }
} catch(PDOException $e) {
    die('خطأ في جلب بيانات الجدول: ' . $e->getMessage());
}

// جلب التعيينات الأصلية
try {
    $stmt = $conn->prepare("SELECT * FROM guard_assignments WHERE schedule_id = ?");
    $stmt->execute([$originalId]);
    $originalAssignments = $stmt->fetchAll();
} catch(PDOException $e) {
    die('خطأ في جلب التعيينات: ' . $e->getMessage());
}

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $scheduleName = $_POST['schedule_name'] ?? '';
    $examDate = $_POST['exam_date'] ?? '';
    $examPeriod = $_POST['exam_period'] ?? '';
    $examType = $_POST['exam_type'] ?? '';
    $notes = $_POST['notes'] ?? '';
    $copyAssignments = isset($_POST['copy_assignments']);
    
    if (empty($scheduleName) || empty($examDate) || empty($examPeriod) || empty($examType)) {
        $error = 'جميع الحقول مطلوبة';
    } else {
        try {
            $conn->beginTransaction();
            
            // إنشاء الجدول الجديد
            $stmt = $conn->prepare("
                INSERT INTO guard_schedules (schedule_name, exam_date, exam_period, exam_type, notes, status, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, 'draft', NOW(), NOW())
            ");
            $stmt->execute([$scheduleName, $examDate, $examPeriod, $examType, $notes]);
            $newScheduleId = $conn->lastInsertId();
            
            // نسخ التعيينات إذا كان مطلوباً
            if ($copyAssignments && !empty($originalAssignments)) {
                $stmt = $conn->prepare("
                    INSERT INTO guard_assignments (schedule_id, guard_id, room_number, guard_type, guard_order, created_at, updated_at) 
                    VALUES (?, ?, ?, ?, ?, NOW(), NOW())
                ");
                
                foreach ($originalAssignments as $assignment) {
                    $stmt->execute([
                        $newScheduleId,
                        $assignment['guard_id'],
                        $assignment['room_number'],
                        $assignment['guard_type'],
                        $assignment['guard_order']
                    ]);
                }
            }
            
            $conn->commit();
            header("Location: view.php?id=$newScheduleId&copied=1");
            exit();
            
        } catch(PDOException $e) {
            $conn->rollBack();
            $error = 'خطأ في نسخ الجدول: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نسخ جدول الحراسة</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../../assets/css/style.css">
</head>
<body>
    <div class="container">
        <div class="page-header">
            <h1>📋 نسخ جدول الحراسة</h1>
            <p>إنشاء نسخة جديدة من الجدول مع إمكانية التعديل</p>
            <a href="view.php?id=<?= $originalId ?>" class="btn btn-secondary">← العودة للجدول الأصلي</a>
        </div>

        <?php if (isset($error)): ?>
        <div class="alert alert-error">
            <strong>خطأ!</strong> <?= htmlspecialchars($error) ?>
        </div>
        <?php endif; ?>

        <div class="form-container">
            <div class="original-info">
                <h3>📄 معلومات الجدول الأصلي</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="label">اسم الجدول:</span>
                        <span class="value"><?= htmlspecialchars($originalSchedule['schedule_name']) ?></span>
                    </div>
                    <div class="info-item">
                        <span class="label">تاريخ الامتحان:</span>
                        <span class="value"><?= date('Y-m-d', strtotime($originalSchedule['exam_date'])) ?></span>
                    </div>
                    <div class="info-item">
                        <span class="label">فترة الامتحان:</span>
                        <span class="value"><?= $originalSchedule['exam_period'] == 'morning' ? 'الصباحية' : 'المسائية' ?></span>
                    </div>
                    <div class="info-item">
                        <span class="label">نوع الامتحان:</span>
                        <span class="value"><?= $originalSchedule['exam_type'] == 'bem' ? 'شهادة التعليم المتوسط' : 'شهادة البكالوريا' ?></span>
                    </div>
                </div>
            </div>

            <form method="POST" class="schedule-form">
                <h3>✏️ بيانات النسخة الجديدة</h3>
                
                <div class="form-group">
                    <label for="schedule_name">اسم الجدول الجديد</label>
                    <input type="text" id="schedule_name" name="schedule_name" 
                           value="نسخة من <?= htmlspecialchars($originalSchedule['schedule_name']) ?>" 
                           required>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="exam_date">تاريخ الامتحان</label>
                        <input type="date" id="exam_date" name="exam_date" 
                               value="<?= $originalSchedule['exam_date'] ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="exam_period">فترة الامتحان</label>
                        <select id="exam_period" name="exam_period" required>
                            <option value="morning" <?= $originalSchedule['exam_period'] == 'morning' ? 'selected' : '' ?>>الصباحية</option>
                            <option value="evening" <?= $originalSchedule['exam_period'] == 'evening' ? 'selected' : '' ?>>المسائية</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="exam_type">نوع الامتحان</label>
                    <select id="exam_type" name="exam_type" required>
                        <option value="bem" <?= $originalSchedule['exam_type'] == 'bem' ? 'selected' : '' ?>>شهادة التعليم المتوسط</option>
                        <option value="bac" <?= $originalSchedule['exam_type'] == 'bac' ? 'selected' : '' ?>>شهادة البكالوريا</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="notes">ملاحظات</label>
                    <textarea id="notes" name="notes" rows="4"><?= htmlspecialchars($originalSchedule['notes']) ?></textarea>
                </div>

                <div class="checkbox-container">
                    <label class="checkbox-label">
                        <input type="checkbox" name="copy_assignments" checked>
                        <div class="checkbox-text">
                            <strong>نسخ تعيينات الحراس من الجدول الأصلي</strong>
                            <small>إذا تم إلغاء التحديد، سيتم إنشاء جدول فارغ بدون تعيينات</small>
                        </div>
                    </label>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">📋 إنشاء النسخة</button>
                    <a href="view.php?id=<?= $originalId ?>" class="btn btn-secondary">إلغاء</a>
                </div>
            </form>
        </div>
    </div>

    <style>
        .page-header {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            color: white;
            padding: 30px;
            border-radius: var(--border-radius);
            margin-bottom: 30px;
            text-align: center;
        }

        .page-header h1 {
            margin: 0 0 10px 0;
            font-size: 2rem;
            font-weight: 700;
        }

        .page-header p {
            margin: 0 0 20px 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .page-header .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
        }

        .page-header .btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .original-info {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            border: 2px solid var(--primary-color);
            border-radius: var(--border-radius);
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .original-info h3 {
            color: var(--primary-color);
            margin-bottom: 20px;
            font-size: 1.3rem;
            font-weight: 700;
            text-align: center;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .info-item .label {
            font-weight: 600;
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .info-item .value {
            color: var(--primary-color);
            font-weight: 600;
            font-size: 1.1rem;
        }

        .form-container {
            background: white;
            border-radius: var(--border-radius);
            padding: 30px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
        }

        .form-container h3 {
            color: var(--primary-color);
            margin-bottom: 25px;
            font-size: 1.3rem;
            font-weight: 700;
            text-align: center;
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--dark-color);
            font-size: 1rem;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            font-size: 1rem;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .checkbox-container {
            background: linear-gradient(135deg, #fef3c7, #fbbf24);
            border: 2px solid #f59e0b;
            border-radius: var(--border-radius);
            padding: 20px;
            margin: 25px 0;
        }

        .checkbox-label {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .checkbox-label input[type="checkbox"] {
            margin: 0;
            width: 18px;
            height: 18px;
            accent-color: var(--primary-color);
        }

        .checkbox-text {
            flex: 1;
        }

        .checkbox-text strong {
            color: #92400e;
            font-weight: 700;
            font-size: 1.1rem;
        }

        .checkbox-label small {
            display: block;
            color: #92400e;
            font-size: 0.9rem;
            margin-top: 5px;
            font-style: italic;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }

        .form-actions .btn {
            padding: 12px 30px;
            font-size: 1rem;
            font-weight: 600;
        }

        .alert {
            padding: 15px 20px;
            border-radius: var(--border-radius);
            margin-bottom: 25px;
            border: 1px solid;
        }

        .alert-error {
            background: #fef2f2;
            border-color: #fecaca;
            color: #dc2626;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }

            .form-actions {
                flex-direction: column;
            }
        }
    </style>
</body>
</html>
