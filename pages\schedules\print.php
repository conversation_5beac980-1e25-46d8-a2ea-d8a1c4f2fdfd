<?php
/**
 * صفحة طباعة جدول الحراسة
 */

require_once '../../config/database.php';

$database = new Database();
$conn = $database->getConnection();

$scheduleId = $_GET['id'] ?? null;

if (!$scheduleId) {
    header('Location: index.php');
    exit();
}

// جلب بيانات الجدول
try {
    $stmt = $conn->prepare("SELECT * FROM guard_schedules WHERE id = ?");
    $stmt->execute([$scheduleId]);
    $schedule = $stmt->fetch();
    
    if (!$schedule) {
        header('Location: index.php');
        exit();
    }
} catch(PDOException $e) {
    die('خطأ في جلب بيانات الجدول: ' . $e->getMessage());
}

// جلب التعيينات
try {
    $stmt = $conn->prepare("
        SELECT ga.*, g.first_name, g.last_name, g.guard_number, g.institution 
        FROM guard_assignments ga 
        JOIN guards g ON ga.guard_id = g.id 
        WHERE ga.schedule_id = ? 
        ORDER BY ga.room_number, ga.guard_type, ga.guard_order
    ");
    $stmt->execute([$scheduleId]);
    $assignments = $stmt->fetchAll();
} catch(PDOException $e) {
    die('خطأ في جلب التعيينات: ' . $e->getMessage());
}

// تنظيم التعيينات حسب القاعة
$roomAssignments = [];
foreach ($assignments as $assignment) {
    $roomAssignments[$assignment['room_number']][] = $assignment;
}

// جلب إعدادات المركز
try {
    $stmt = $conn->prepare("SELECT * FROM center_settings LIMIT 1");
    $stmt->execute();
    $centerSettings = $stmt->fetch() ?: [];
} catch(PDOException $e) {
    $centerSettings = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة جدول الحراسة - <?= htmlspecialchars($schedule['schedule_name']) ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #10b981;
            --accent-color: #f59e0b;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --border-color: #e2e8f0;
            --border-radius: 8px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: white;
            color: var(--dark-color);
            line-height: 1.4;
            direction: rtl;
            padding: 15px;
            font-size: 14px;
        }

        .print-container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
        }

        .main-frame {
            border: 3px solid black;
            padding: 15px;
            margin: 10px;
            background: white;
            min-height: calc(100vh - 40px);
            position: relative;
        }

        .print-header {
            text-align: center;
            margin-bottom: 20px;
            padding: 12px;
            background: white;
            border: 2px solid black;
            color: black;
        }

        .header-content {
            margin: 0 auto;
        }

        .ministry-name {
            font-size: 1.1rem;
            font-weight: 500;
            margin-bottom: 4px;
            color: black;
            font-family: 'Cairo', sans-serif;
        }

        .header-details {
            display: flex;
            justify-content: space-between;
            margin: 15px 0;
            font-size: 1rem;
            font-weight: 400;
            font-family: 'Cairo', sans-serif;
        }

        .right-section, .left-section {
            text-align: center;
            font-family: 'Cairo', sans-serif;
        }

        .right-section div, .left-section div {
            margin-bottom: 3px;
            font-size: 1rem;
        }

        .print-title {
            font-size: 2rem;
            font-weight: 600;
            margin: 15px 0;
            color: black;
            text-decoration: underline;
            font-family: 'Cairo', sans-serif;
        }

        .exam-details {
            margin-top: 10px;
        }

        .exam-date {
            font-size: 1.4rem;
            font-weight: 500;
            margin-bottom: 8px;
            font-family: 'Cairo', sans-serif;
        }

        .exam-period {
            font-size: 1.2rem;
            font-weight: 500;
            margin-bottom: 10px;
            color: #333;
            font-family: 'Cairo', sans-serif;
        }



        .schedule-info {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
            background: var(--light-color);
            padding: 15px;
            border-radius: var(--border-radius);
        }

        .info-item {
            text-align: center;
            padding: 5px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }

        .info-label {
            font-weight: 600;
            color: var(--dark-color);
            font-size: 0.8rem;
            display: block;
            margin-bottom: 3px;
        }

        .info-value {
            color: var(--primary-color);
            font-weight: 500;
            font-size: 0.9rem;
        }

        .table-section {
            margin-top: 15px;
            margin-bottom: 60px;
        }

        .simple-table {
            width: 100%;
            border-collapse: collapse;
            border: 2px solid black;
        }

        .simple-table th,
        .simple-table td {
            border: 1px solid black;
            padding: 8px;
            text-align: center;
            font-size: 1.2rem;
            font-family: 'Arial Black', Arial, sans-serif;
            line-height: 1.2;
        }

        .simple-table th {
            background: white;
            font-weight: 600;
            font-size: 1.1rem;
            font-family: 'Cairo', sans-serif;
        }

        .room-number-cell {
            font-weight: 700;
            background: white;
            font-size: 1.8rem;
            font-family: 'Arial', Arial, sans-serif;
        }

        .main-guard-cell {
            background: white;
            color: red;
            font-weight: 700;
            font-size: 2rem;
            font-family: 'Arial', Arial, sans-serif;
        }

        .regular-guard-cell {
            background: white;
            font-weight: 700;
            font-size: 1.8rem;
            font-family: 'Arial', Arial, sans-serif;
        }

        .signature-section {
            position: absolute;
            bottom: 20px;
            left: 20px;
            text-align: left;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .signature-title {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .signature-name {
            font-weight: 400;
            text-decoration: underline;
        }

        .guard-info {
            text-align: center;
            line-height: 1.3;
            padding: 4px;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.8);
        }

        .main-guard .guard-info {
            background: linear-gradient(135deg, #fef3c7, #fbbf24);
            border: 1px solid #f59e0b;
        }

        .regular-guard .guard-info {
            background: linear-gradient(135deg, #e0f2fe, #0ea5e9);
            border: 1px solid #0284c7;
        }

        .guard-name {
            font-weight: 700;
            margin-bottom: 3px;
            font-size: 0.75rem;
            color: #1f2937;
        }

        .guard-number {
            font-size: 0.65rem;
            color: #4b5563;
            margin-bottom: 2px;
            font-weight: 500;
        }

        .guard-institution {
            font-size: 0.6rem;
            color: #6b7280;
            font-weight: 400;
        }

        .empty-cell {
            color: #9ca3af;
            font-style: italic;
            font-size: 0.7rem;
            padding: 8px;
            background: #f9fafb;
            border-radius: 4px;
        }

        .stats-summary {
            background: var(--light-color);
            border-radius: var(--border-radius);
            padding: 12px;
            margin-bottom: 15px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
        }

        .stat-item {
            text-align: center;
            padding: 8px;
            background: white;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
        }

        .stat-number {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 3px;
        }

        .stat-label {
            font-size: 0.7rem;
            color: #666;
        }

        .notes-section {
            background: #fef3c7;
            border: 1px solid var(--accent-color);
            border-radius: var(--border-radius);
            padding: 10px;
            margin-top: 10px;
        }

        .notes-title {
            color: #92400e;
            margin-bottom: 5px;
            font-weight: 600;
            font-size: 0.8rem;
        }

        .notes-content {
            color: #92400e;
            font-size: 0.7rem;
        }

        .print-actions {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: var(--light-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
        }

        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 0 8px;
            border: none;
            border-radius: var(--border-radius);
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            text-align: center;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        .btn-success {
            background: var(--secondary-color);
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .print-footer {
            text-align: center;
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px solid var(--border-color);
            color: #666;
            font-size: 0.7rem;
        }

        /* Print-specific styles */
        @media print {
            .print-actions {
                display: none !important;
            }

            body {
                padding: 0.3cm !important;
                background: white !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                font-size: 12px !important;
            }

            .print-container {
                max-width: none;
                margin: 0;
            }

            .main-frame {
                border: 3px solid black !important;
                padding: 12px !important;
                margin: 0 !important;
                min-height: calc(100vh - 20px) !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .table-section {
                margin-bottom: 50px !important;
            }

            .signature-section {
                position: absolute !important;
                bottom: 15px !important;
                left: 15px !important;
            }

            .print-header {
                margin-bottom: 15px;
                padding: 10px !important;
                background: white !important;
                border: 2px solid black !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .ministry-name {
                font-size: 1rem !important;
                color: black !important;
                font-family: 'Cairo', sans-serif !important;
                font-weight: 500 !important;
            }

            .header-details {
                font-size: 0.9rem !important;
                font-weight: 400 !important;
                font-family: 'Cairo', sans-serif !important;
            }

            .right-section div, .left-section div {
                font-size: 0.9rem !important;
                font-family: 'Cairo', sans-serif !important;
            }

            .print-title {
                font-size: 1.6rem !important;
                margin: 10px 0 !important;
                color: black !important;
                font-family: 'Cairo', sans-serif !important;
                font-weight: 600 !important;
            }

            .exam-date {
                font-size: 1.1rem !important;
                font-family: 'Cairo', sans-serif !important;
                font-weight: 500 !important;
            }

            .exam-period {
                font-size: 1rem !important;
                color: black !important;
                font-family: 'Cairo', sans-serif !important;
                font-weight: 500 !important;
            }



            .simple-table {
                border: 2px solid black !important;
            }

            .simple-table th,
            .simple-table td {
                border: 1px solid black !important;
                font-size: 1.1rem !important;
                padding: 12px !important;
                font-family: 'Arial Black', Arial, sans-serif !important;
            }

            .simple-table th {
                font-family: 'Cairo', sans-serif !important;
                font-weight: 600 !important;
            }

            .room-number-cell {
                font-size: 1.6rem !important;
                font-weight: 700 !important;
                font-family: 'Arial', Arial, sans-serif !important;
            }

            .main-guard-cell {
                font-size: 1.8rem !important;
                font-weight: 700 !important;
                color: red !important;
                font-family: 'Arial', Arial, sans-serif !important;
            }

            .regular-guard-cell {
                font-size: 1.6rem !important;
                font-weight: 700 !important;
                font-family: 'Arial', Arial, sans-serif !important;
            }

            .schedule-info {
                margin-bottom: 15px;
                padding: 10px;
            }

            .guards-table {
                page-break-inside: auto;
                margin-bottom: 10px;
                font-size: 0.65rem !important;
            }

            .guards-table th,
            .guards-table td {
                padding: 4px 2px !important;
            }

            .guard-name {
                font-size: 0.65rem !important;
            }

            .guard-number {
                font-size: 0.55rem !important;
            }

            .guard-institution {
                font-size: 0.5rem !important;
            }

            .guards-table th {
                background: #2563eb !important;
                color: white !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .room-number {
                background: #2563eb !important;
                color: white !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .main-guard .guard-info {
                background: #fef3c7 !important;
                border: 1px solid #f59e0b !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .regular-guard .guard-info {
                background: #e0f2fe !important;
                border: 1px solid #0284c7 !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .stats-summary {
                page-break-inside: avoid;
                margin-bottom: 15px;
                padding: 10px;
            }

            .notes-section {
                page-break-inside: avoid;
                margin-top: 10px;
                padding: 8px;
            }

            .print-footer {
                margin-top: 10px;
                padding-top: 8px;
            }
        }

        @page {
            margin: 0.8cm;
            size: A4 portrait;
        }
    </style>
</head>
<body>
    <div class="print-container">
        <div class="print-actions">
            <h3 style="margin-bottom: 20px; color: var(--primary-color);">🖨️ طباعة جدول الحراسة</h3>
            <p style="color: #666; margin-bottom: 20px;">اضغط على زر الطباعة أدناه أو استخدم Ctrl+P لطباعة الجدول</p>
            <button onclick="window.print()" class="btn btn-primary">🖨️ طباعة الجدول</button>
            <a href="view.php?id=<?= $scheduleId ?>" class="btn btn-secondary">👁️ عرض الجدول</a>
            <a href="index.php" class="btn btn-success">📋 قائمة الجداول</a>
        </div>

        <div class="main-frame">
            <div class="print-header">
            <div class="header-content">
                <div class="ministry-name">الجمهورية الجزائرية الديمقراطية الشعبية</div>
                <div class="ministry-name">وزارة التربية الوطنية</div>

                <div class="header-details">
                    <div class="right-section">
                        <div>مركز الإجراء: <?= htmlspecialchars($centerSettings['center_name'] ?? 'مركز الامتحانات') ?></div>
                        <div>رمز المركز: <?= htmlspecialchars($centerSettings['center_code'] ?? '00000') ?></div>
                    </div>
                    <div class="left-section">
                        <div>دورة: <?php
                            $examMonth = date('n', strtotime($schedule['exam_date']));
                            $examYear = date('Y', strtotime($schedule['exam_date']));
                            if ($examMonth >= 5 && $examMonth <= 7) {
                                echo "جوان $examYear";
                            } else {
                                echo date('F Y', strtotime($schedule['exam_date']));
                            }
                        ?></div>
                        <div>امتحان <?= $schedule['exam_type'] == 'bem' ? 'شهادة التعليم المتوسط' : 'شهادة البكالوريا' ?></div>
                    </div>
                </div>

                <div class="print-title">جدول الحراسة</div>

                <div class="exam-details">
                    <div class="exam-date">
                        <?php
                        $examDate = date('Y-m-d', strtotime($schedule['exam_date']));
                        $dayName = '';
                        switch (date('w', strtotime($schedule['exam_date']))) {
                            case 0: $dayName = 'الأحد'; break;
                            case 1: $dayName = 'الاثنين'; break;
                            case 2: $dayName = 'الثلاثاء'; break;
                            case 3: $dayName = 'الأربعاء'; break;
                            case 4: $dayName = 'الخميس'; break;
                            case 5: $dayName = 'الجمعة'; break;
                            case 6: $dayName = 'السبت'; break;
                        }
                        echo "يوم $dayName $examDate";
                        ?>
                    </div>
                    <div class="exam-period">
                        الفترة: <?= $schedule['exam_period'] == 'morning' ? 'الصباحية' : 'المسائية' ?>
                    </div>
                </div>
            </div>
        </div>

            <div class="table-section">
                <table class="simple-table">
            <thead>
                <tr>
                    <th>رقم القاعة</th>
                    <th>الحارس الرئيسي</th>
                    <th>الحارس 02</th>
                    <th>الحارس 03</th>
                </tr>
            </thead>
            <tbody>
                <?php for ($room = 1; $room <= ($centerSettings['rooms_count'] ?? 14); $room++): ?>
                <?php
                $roomNumber = str_pad($room, 2, '0', STR_PAD_LEFT);
                $roomGuards = $roomAssignments[$roomNumber] ?? [];

                $mainGuard = null;
                $guard2 = null;
                $guard3 = null;

                foreach ($roomGuards as $guard) {
                    if ($guard['guard_type'] == 'main') {
                        $mainGuard = $guard;
                    } elseif ($guard['guard_type'] == 'regular') {
                        if ($guard['guard_order'] == 2) $guard2 = $guard;
                        if ($guard['guard_order'] == 3) $guard3 = $guard;
                    }
                }
                ?>
                <tr>
                    <td class="room-number-cell"><?= $roomNumber ?></td>

                    <td class="main-guard-cell">
                        <?php if ($mainGuard): ?>
                            <?= htmlspecialchars($mainGuard['guard_number']) ?>
                        <?php else: ?>

                        <?php endif; ?>
                    </td>

                    <td class="regular-guard-cell">
                        <?php if ($guard2): ?>
                            <?= htmlspecialchars($guard2['guard_number']) ?>
                        <?php else: ?>

                        <?php endif; ?>
                    </td>

                    <td class="regular-guard-cell">
                        <?php if ($guard3): ?>
                            <?= htmlspecialchars($guard3['guard_number']) ?>
                        <?php else: ?>

                        <?php endif; ?>
                    </td>
                </tr>
                <?php endfor; ?>
            </tbody>
                </table>
            </div>

            <div class="signature-section">
                <div class="signature-title">رئيس المركز</div>
                <div class="signature-name"><?= htmlspecialchars($centerSettings['head_name'] ?? '') ?></div>
            </div>
        </div>


    </div>

    <script>
        // Print functionality
        function printSchedule() {
            window.print();
        }

        // Add keyboard shortcut for printing (Ctrl+P)
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'p') {
                e.preventDefault();
                printSchedule();
            }
        });

        // Show print preview message
        window.addEventListener('beforeprint', function() {
            console.log('جاري تحضير الطباعة...');
        });

        window.addEventListener('afterprint', function() {
            console.log('تم الانتهاء من الطباعة');
        });

        // Auto-focus on print button when page loads
        window.onload = function() {
            const printBtn = document.querySelector('button[onclick="window.print()"]');
            if (printBtn) {
                printBtn.focus();
            }
        };
    </script>
</body>
</html>
