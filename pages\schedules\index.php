<?php
/**
 * صفحة عرض جداول الحراسة
 */

require_once '../../config/database.php';

$database = new Database();
$conn = $database->getConnection();

$message = '';
$messageType = '';

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'delete_schedule':
                try {
                    $stmt = $conn->prepare("DELETE FROM guard_schedules WHERE id = ?");
                    $stmt->execute([$_POST['schedule_id']]);
                    $message = 'تم حذف الجدول بنجاح';
                    $messageType = 'success';
                } catch(PDOException $e) {
                    $message = 'خطأ في حذف الجدول: ' . $e->getMessage();
                    $messageType = 'error';
                }
                break;

            case 'publish_schedule':
                try {
                    $stmt = $conn->prepare("UPDATE guard_schedules SET status = 'published' WHERE id = ?");
                    $stmt->execute([$_POST['schedule_id']]);
                    $message = 'تم نشر الجدول بنجاح';
                    $messageType = 'success';
                } catch(PDOException $e) {
                    $message = 'خطأ في نشر الجدول: ' . $e->getMessage();
                    $messageType = 'error';
                }
                break;
        }
    }
}

// جلب الجداول مع الإحصائيات
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 10;
$offset = ($page - 1) * $limit;

$status_filter = $_GET['status'] ?? '';
$exam_type_filter = $_GET['exam_type'] ?? '';
$search = $_GET['search'] ?? '';

$where_conditions = [];
$params = [];

if ($status_filter) {
    $where_conditions[] = "gs.status = ?";
    $params[] = $status_filter;
}

if ($exam_type_filter) {
    $where_conditions[] = "gs.exam_type = ?";
    $params[] = $exam_type_filter;
}

if ($search) {
    $where_conditions[] = "(gs.schedule_name LIKE ? OR gs.exam_date LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param]);
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// عدد الجداول الإجمالي
$count_stmt = $conn->prepare("SELECT COUNT(*) as total FROM guard_schedules gs $where_clause");
$count_stmt->execute($params);
$total_schedules = $count_stmt->fetch()['total'];
$total_pages = ceil($total_schedules / $limit);

// جلب الجداول مع إحصائيات الحراس
$stmt = $conn->prepare("
    SELECT gs.*,
           COUNT(ga.id) as total_assignments,
           COUNT(CASE WHEN ga.guard_type = 'main' THEN 1 END) as main_guards_count,
           COUNT(CASE WHEN ga.guard_type = 'regular' THEN 1 END) as regular_guards_count
    FROM guard_schedules gs
    LEFT JOIN guard_assignments ga ON gs.id = ga.schedule_id
    $where_clause
    GROUP BY gs.id
    ORDER BY gs.created_at DESC
    LIMIT $limit OFFSET $offset
");
$stmt->execute($params);
$schedules = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جداول الحراسة - نظام إدارة حراسة الامتحانات</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .schedules-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .filters-section {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
            margin-bottom: 20px;
        }

        .filter-input, .filter-select {
            padding: 10px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
        }

        .schedules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 20px;
        }

        .schedule-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--border-color);
            transition: var(--transition);
            position: relative;
        }

        .schedule-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-medium);
        }

        .schedule-header {
            margin-bottom: 15px;
        }

        .schedule-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 8px;
        }

        .schedule-date {
            color: var(--primary-color);
            font-weight: 600;
            font-size: 1rem;
        }

        .schedule-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .info-label {
            font-size: 0.8rem;
            color: #666;
            font-weight: 500;
        }

        .info-value {
            font-weight: 600;
            color: var(--dark-color);
        }

        .schedule-stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
            padding: 15px;
            background: var(--light-color);
            border-radius: 8px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 0.8rem;
            color: #666;
        }

        .schedule-status {
            position: absolute;
            top: 15px;
            left: 15px;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-draft {
            background: #fef3c7;
            color: #92400e;
        }

        .status-completed {
            background: #d1fae5;
            color: #065f46;
        }

        .status-published {
            background: #dbeafe;
            color: #1e40af;
        }

        .schedule-actions {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .btn-xs {
            padding: 6px 12px;
            font-size: 0.8rem;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-state-icon {
            font-size: 4rem;
            margin-bottom: 20px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 30px;
        }

        .pagination a,
        .pagination span {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            text-decoration: none;
            color: var(--dark-color);
        }

        .pagination .current {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        @media (max-width: 768px) {
            .schedules-grid {
                grid-template-columns: 1fr;
            }

            .schedule-info {
                grid-template-columns: 1fr;
            }

            .filters-section {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 جداول الحراسة</h1>
            <p>عرض وإدارة جداول الحراسة المحفوظة</p>
            <a href="../../index.php" class="btn btn-secondary">← العودة للرئيسية</a>
        </div>

        <?php if ($message): ?>
        <div class="alert alert-<?= $messageType ?>">
            <?= htmlspecialchars($message) ?>
        </div>
        <?php endif; ?>

        <div class="schedules-header">
            <div>
                <a href="create.php" class="btn btn-success">➕ إنشاء جدول جديد</a>
            </div>

            <div style="color: #666;">
                إجمالي الجداول: <strong><?= $total_schedules ?></strong>
            </div>
        </div>

        <div class="filters-section">
            <form method="GET" style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                <input type="text" name="search" placeholder="البحث في اسم الجدول أو التاريخ..."
                       value="<?= htmlspecialchars($search) ?>" class="filter-input" style="min-width: 250px;">

                <select name="status" class="filter-select">
                    <option value="">جميع الحالات</option>
                    <option value="draft" <?= $status_filter == 'draft' ? 'selected' : '' ?>>مسودة</option>
                    <option value="completed" <?= $status_filter == 'completed' ? 'selected' : '' ?>>مكتمل</option>
                    <option value="published" <?= $status_filter == 'published' ? 'selected' : '' ?>>منشور</option>
                </select>

                <select name="exam_type" class="filter-select">
                    <option value="">جميع أنواع الامتحانات</option>
                    <option value="bem" <?= $exam_type_filter == 'bem' ? 'selected' : '' ?>>شهادة التعليم المتوسط</option>
                    <option value="bac" <?= $exam_type_filter == 'bac' ? 'selected' : '' ?>>شهادة البكالوريا</option>
                </select>

                <button type="submit" class="btn btn-primary">بحث</button>
                <a href="?" class="btn btn-secondary">مسح</a>
            </form>
        </div>

        <?php if (empty($schedules)): ?>
        <div class="empty-state">
            <div class="empty-state-icon">📋</div>
            <h3>لا توجد جداول حراسة</h3>
            <p>لم يتم إنشاء أي جداول حراسة بعد</p>
            <a href="create.php" class="btn btn-primary" style="margin-top: 20px;">إنشاء أول جدول</a>
        </div>
        <?php else: ?>
        <div class="schedules-grid">
            <?php foreach ($schedules as $schedule): ?>
            <div class="schedule-card">
                <div class="schedule-status status-<?= $schedule['status'] ?>">
                    <?php
                    $statusLabels = [
                        'draft' => 'مسودة',
                        'completed' => 'مكتمل',
                        'published' => 'منشور'
                    ];
                    echo $statusLabels[$schedule['status']] ?? $schedule['status'];
                    ?>
                </div>

                <div class="schedule-header">
                    <div class="schedule-title"><?= htmlspecialchars($schedule['schedule_name']) ?></div>
                    <div class="schedule-date">
                        📅 <?= date('Y-m-d', strtotime($schedule['exam_date'])) ?>
                        - <?= $schedule['exam_period'] == 'morning' ? 'الصباحية' : 'المسائية' ?>
                    </div>
                </div>

                <div class="schedule-info">
                    <div class="info-item">
                        <span class="info-label">نوع الامتحان</span>
                        <span class="info-value">
                            <?= $schedule['exam_type'] == 'bem' ? 'شهادة التعليم المتوسط' : 'شهادة البكالوريا' ?>
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">تاريخ الإنشاء</span>
                        <span class="info-value"><?= date('Y-m-d', strtotime($schedule['created_at'])) ?></span>
                    </div>
                </div>

                <div class="schedule-stats">
                    <div class="stat-item">
                        <div class="stat-number"><?= $schedule['total_assignments'] ?></div>
                        <div class="stat-label">إجمالي التعيينات</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><?= $schedule['main_guards_count'] ?></div>
                        <div class="stat-label">حراس رئيسيين</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number"><?= $schedule['regular_guards_count'] ?></div>
                        <div class="stat-label">حراس عاديين</div>
                    </div>
                </div>

                <div class="schedule-actions">
                    <a href="view.php?id=<?= $schedule['id'] ?>" class="btn btn-xs btn-primary">👁️ عرض</a>
                    <a href="print.php?id=<?= $schedule['id'] ?>" class="btn btn-xs btn-info" target="_blank">🖨️ طباعة</a>

                    <?php if ($schedule['status'] == 'completed'): ?>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="publish_schedule">
                        <input type="hidden" name="schedule_id" value="<?= $schedule['id'] ?>">
                        <button type="submit" class="btn btn-xs btn-success" onclick="return confirm('هل تريد نشر هذا الجدول؟')">
                            📢 نشر
                        </button>
                    </form>
                    <?php endif; ?>

                    <a href="edit.php?id=<?= $schedule['id'] ?>" class="btn btn-xs btn-warning">✏️ تعديل</a>

                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="delete_schedule">
                        <input type="hidden" name="schedule_id" value="<?= $schedule['id'] ?>">
                        <button type="submit" class="btn btn-xs btn-danger" onclick="return confirm('هل أنت متأكد من حذف هذا الجدول؟ سيتم حذف جميع التعيينات المرتبطة به.')">
                            🗑️ حذف
                        </button>
                    </form>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <?php if ($total_pages > 1): ?>
        <div class="pagination">
            <?php if ($page > 1): ?>
            <a href="?page=<?= $page - 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status_filter) ?>&exam_type=<?= urlencode($exam_type_filter) ?>">السابق</a>
            <?php endif; ?>

            <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
            <?php if ($i == $page): ?>
            <span class="current"><?= $i ?></span>
            <?php else: ?>
            <a href="?page=<?= $i ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status_filter) ?>&exam_type=<?= urlencode($exam_type_filter) ?>"><?= $i ?></a>
            <?php endif; ?>
            <?php endfor; ?>

            <?php if ($page < $total_pages): ?>
            <a href="?page=<?= $page + 1 ?>&search=<?= urlencode($search) ?>&status=<?= urlencode($status_filter) ?>&exam_type=<?= urlencode($exam_type_filter) ?>">التالي</a>
            <?php endif; ?>
        </div>
        <?php endif; ?>
        <?php endif; ?>
    </div>
</body>
</html>
