<?php
/**
 * صفحة طباعة جدول الحراسة
 */

require_once '../../config/database.php';

$database = new Database();
$conn = $database->getConnection();

$scheduleId = $_GET['id'] ?? null;

if (!$scheduleId) {
    header('Location: index.php');
    exit();
}

// جلب بيانات الجدول
try {
    $stmt = $conn->prepare("SELECT * FROM guard_schedules WHERE id = ?");
    $stmt->execute([$scheduleId]);
    $schedule = $stmt->fetch();
    
    if (!$schedule) {
        header('Location: index.php');
        exit();
    }
} catch(PDOException $e) {
    die('خطأ في جلب بيانات الجدول: ' . $e->getMessage());
}

// جلب التعيينات
try {
    $stmt = $conn->prepare("
        SELECT ga.*, g.first_name, g.last_name, g.guard_number, g.institution 
        FROM guard_assignments ga 
        JOIN guards g ON ga.guard_id = g.id 
        WHERE ga.schedule_id = ? 
        ORDER BY ga.room_number, ga.guard_type, ga.guard_order
    ");
    $stmt->execute([$scheduleId]);
    $assignments = $stmt->fetchAll();
} catch(PDOException $e) {
    die('خطأ في جلب التعيينات: ' . $e->getMessage());
}

// تنظيم التعيينات حسب القاعة
$roomAssignments = [];
foreach ($assignments as $assignment) {
    $roomAssignments[$assignment['room_number']][] = $assignment;
}

// جلب إعدادات المركز
try {
    $stmt = $conn->prepare("SELECT * FROM center_settings LIMIT 1");
    $stmt->execute();
    $centerSettings = $stmt->fetch() ?: [];
} catch(PDOException $e) {
    $centerSettings = [];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طباعة جدول الحراسة - <?= htmlspecialchars($schedule['schedule_name']) ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #10b981;
            --accent-color: #f59e0b;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --border-color: #e2e8f0;
            --border-radius: 8px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: white;
            color: var(--dark-color);
            line-height: 1.6;
            direction: rtl;
            padding: 20px;
        }

        .print-container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
        }

        .print-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid var(--border-color);
        }

        .print-title {
            font-size: 1.8rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .print-subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 15px;
        }

        .schedule-info {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 25px;
            background: var(--light-color);
            padding: 20px;
            border-radius: var(--border-radius);
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: var(--dark-color);
        }

        .info-value {
            color: var(--primary-color);
            font-weight: 500;
        }

        .guards-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 25px;
            border: 2px solid var(--border-color);
        }

        .guards-table th,
        .guards-table td {
            padding: 12px 8px;
            text-align: center;
            border: 1px solid var(--border-color);
            font-size: 0.9rem;
        }

        .guards-table th {
            background: var(--primary-color);
            color: white;
            font-weight: 600;
        }

        .guards-table tbody tr:nth-child(even) {
            background: #f8fafc;
        }

        .room-number {
            font-weight: 700;
            color: var(--primary-color);
            background: var(--light-color) !important;
        }

        .guard-info {
            text-align: center;
        }

        .guard-name {
            font-weight: 600;
            margin-bottom: 3px;
        }

        .guard-number {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 2px;
        }

        .guard-institution {
            font-size: 0.75rem;
            color: #888;
        }

        .empty-cell {
            color: #999;
            font-style: italic;
        }

        .stats-summary {
            background: var(--light-color);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 25px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }

        .notes-section {
            background: #fef3c7;
            border: 1px solid var(--accent-color);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-top: 20px;
        }

        .notes-title {
            color: #92400e;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .notes-content {
            color: #92400e;
        }

        .print-footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
            color: #666;
            font-size: 0.9rem;
        }

        /* Print-specific styles */
        @media print {
            body {
                padding: 0;
                background: white !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .print-container {
                max-width: none;
                margin: 0;
            }

            .guards-table {
                page-break-inside: avoid;
            }

            .guards-table th {
                background: #2563eb !important;
                color: white !important;
            }

            .stats-summary {
                page-break-inside: avoid;
            }

            .notes-section {
                page-break-inside: avoid;
            }
        }

        @page {
            margin: 1cm;
            size: A4;
        }
    </style>
</head>
<body>
    <div class="print-container">
        <div class="print-header">
            <div class="print-title">جدول حراسة الامتحانات</div>
            <div class="print-subtitle"><?= htmlspecialchars($schedule['schedule_name']) ?></div>
        </div>

        <div class="schedule-info">
            <div class="info-item">
                <span class="info-label">تاريخ الامتحان:</span>
                <span class="info-value"><?= date('Y-m-d', strtotime($schedule['exam_date'])) ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">فترة الامتحان:</span>
                <span class="info-value"><?= $schedule['exam_period'] == 'morning' ? 'الصباحية' : 'المسائية' ?></span>
            </div>
            <div class="info-item">
                <span class="info-label">نوع الامتحان:</span>
                <span class="info-value">
                    <?= $schedule['exam_type'] == 'bem' ? 'شهادة التعليم المتوسط' : 'شهادة البكالوريا' ?>
                </span>
            </div>
            <div class="info-item">
                <span class="info-label">تاريخ الطباعة:</span>
                <span class="info-value"><?= date('Y-m-d H:i:s') ?></span>
            </div>
        </div>

        <?php
        // حساب الإحصائيات
        $totalAssignments = count($assignments);
        $mainGuardsCount = count(array_filter($assignments, function($a) { return $a['guard_type'] == 'main'; }));
        $regularGuardsCount = count(array_filter($assignments, function($a) { return $a['guard_type'] == 'regular'; }));
        $roomsWithGuards = count($roomAssignments);
        ?>

        <div class="stats-summary">
            <h3 style="margin-bottom: 15px; color: var(--primary-color); text-align: center;">📊 ملخص الإحصائيات</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number"><?= $totalAssignments ?></div>
                    <div class="stat-label">إجمالي التعيينات</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= $mainGuardsCount ?></div>
                    <div class="stat-label">حراس رئيسيين</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= $regularGuardsCount ?></div>
                    <div class="stat-label">حراس عاديين</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= $roomsWithGuards ?></div>
                    <div class="stat-label">قاعات مُعيَّنة</div>
                </div>
            </div>
        </div>

        <table class="guards-table">
            <thead>
                <tr>
                    <th>رقم القاعة</th>
                    <th>الحارس الرئيسي</th>
                    <th>الحارس العادي الأول</th>
                    <th>الحارس العادي الثاني</th>
                </tr>
            </thead>
            <tbody>
                <?php for ($room = 1; $room <= ($centerSettings['rooms_count'] ?? 14); $room++): ?>
                <?php
                $roomNumber = str_pad($room, 2, '0', STR_PAD_LEFT);
                $roomGuards = $roomAssignments[$roomNumber] ?? [];

                $mainGuard = null;
                $guard2 = null;
                $guard3 = null;

                foreach ($roomGuards as $guard) {
                    if ($guard['guard_type'] == 'main') {
                        $mainGuard = $guard;
                    } elseif ($guard['guard_type'] == 'regular') {
                        if ($guard['guard_order'] == 2) $guard2 = $guard;
                        if ($guard['guard_order'] == 3) $guard3 = $guard;
                    }
                }
                ?>
                <tr>
                    <td class="room-number"><?= $roomNumber ?></td>

                    <td>
                        <?php if ($mainGuard): ?>
                        <div class="guard-info">
                            <div class="guard-name"><?= htmlspecialchars($mainGuard['first_name'] . ' ' . $mainGuard['last_name']) ?></div>
                            <div class="guard-number">رقم: <?= htmlspecialchars($mainGuard['guard_number']) ?></div>
                            <div class="guard-institution"><?= htmlspecialchars($mainGuard['institution']) ?></div>
                        </div>
                        <?php else: ?>
                        <div class="empty-cell">غير معيَّن</div>
                        <?php endif; ?>
                    </td>

                    <td>
                        <?php if ($guard2): ?>
                        <div class="guard-info">
                            <div class="guard-name"><?= htmlspecialchars($guard2['first_name'] . ' ' . $guard2['last_name']) ?></div>
                            <div class="guard-number">رقم: <?= htmlspecialchars($guard2['guard_number']) ?></div>
                            <div class="guard-institution"><?= htmlspecialchars($guard2['institution']) ?></div>
                        </div>
                        <?php else: ?>
                        <div class="empty-cell">غير معيَّن</div>
                        <?php endif; ?>
                    </td>

                    <td>
                        <?php if ($guard3): ?>
                        <div class="guard-info">
                            <div class="guard-name"><?= htmlspecialchars($guard3['first_name'] . ' ' . $guard3['last_name']) ?></div>
                            <div class="guard-number">رقم: <?= htmlspecialchars($guard3['guard_number']) ?></div>
                            <div class="guard-institution"><?= htmlspecialchars($guard3['institution']) ?></div>
                        </div>
                        <?php else: ?>
                        <div class="empty-cell">غير معيَّن</div>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endfor; ?>
            </tbody>
        </table>

        <?php if ($schedule['notes']): ?>
        <div class="notes-section">
            <h4 class="notes-title">📝 ملاحظات:</h4>
            <div class="notes-content"><?= nl2br(htmlspecialchars($schedule['notes'])) ?></div>
        </div>
        <?php endif; ?>

        <div class="print-footer">
            <p>تم إنشاء هذا الجدول بواسطة نظام إدارة حراسة الامتحانات</p>
            <p>آخر تحديث: <?= date('Y-m-d H:i:s', strtotime($schedule['updated_at'])) ?></p>
        </div>
    </div>

    <script>
        // Auto-print when page loads (optional)
        window.onload = function() {
            // Uncomment the next line if you want automatic printing
            // window.print();
        };
    </script>
</body>
</html>
