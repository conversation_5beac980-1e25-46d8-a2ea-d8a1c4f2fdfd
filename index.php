<?php
/**
 * الصفحة الرئيسية - نظام إدارة حراسة الامتحانات
 */

require_once 'config/database.php';

// التحقق من وجود قاعدة البيانات
$database = new Database();
$conn = $database->getConnection();

if (!$conn) {
    header('Location: config/database.php');
    exit();
}

// جلب إعدادات المركز
try {
    $stmt = $conn->prepare("SELECT * FROM center_settings WHERE id = 1");
    $stmt->execute();
    $centerSettings = $stmt->fetch();
} catch(PDOException $e) {
    $centerSettings = null;
}

// جلب إحصائيات سريعة
try {
    // عدد الحراس
    $stmt = $conn->prepare("SELECT COUNT(*) as total_guards FROM guards WHERE is_active = 1");
    $stmt->execute();
    $guardsCount = $stmt->fetch()['total_guards'];

    // عدد الجداول
    $stmt = $conn->prepare("SELECT COUNT(*) as total_schedules FROM guard_schedules");
    $stmt->execute();
    $schedulesCount = $stmt->fetch()['total_schedules'];

    // آخر جدول
    $stmt = $conn->prepare("SELECT * FROM guard_schedules ORDER BY created_at DESC LIMIT 1");
    $stmt->execute();
    $lastSchedule = $stmt->fetch();

} catch(PDOException $e) {
    $guardsCount = 0;
    $schedulesCount = 0;
    $lastSchedule = null;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة حراسة الامتحانات</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #10b981;
            --accent-color: #f59e0b;
            --danger-color: #ef4444;
            --success-color: #22c55e;
            --info-color: #06b6d4;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --border-color: #e2e8f0;
            --shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --border-radius: 12px;
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-attachment: fixed;
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
            line-height: 1.6;
            color: var(--dark-color);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius);
            padding: 30px;
            box-shadow: var(--shadow-medium);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid var(--border-color);
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1rem;
        }

        .center-info {
            background: var(--light-color);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid var(--border-color);
        }

        .center-info h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1.3rem;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .info-label {
            font-weight: 600;
            color: var(--dark-color);
        }

        .info-value {
            color: var(--primary-color);
            font-weight: 500;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            color: white;
            padding: 25px;
            border-radius: var(--border-radius);
            text-align: center;
            box-shadow: var(--shadow-medium);
            transition: var(--transition);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
        }

        .stat-card.success {
            background: linear-gradient(135deg, var(--success-color), #16a34a);
        }

        .stat-card.warning {
            background: linear-gradient(135deg, var(--accent-color), #d97706);
        }

        .stat-card.info {
            background: linear-gradient(135deg, var(--info-color), #0891b2);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .action-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--border-color);
            transition: var(--transition);
            text-decoration: none;
            color: inherit;
        }

        .action-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-medium);
            border-color: var(--primary-color);
        }

        .action-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            text-align: center;
        }

        .action-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 10px;
            text-align: center;
        }

        .action-description {
            color: #666;
            text-align: center;
            font-size: 0.95rem;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            border: none;
            border-radius: var(--border-radius);
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            text-decoration: none;
            text-align: center;
            font-size: 1rem;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #16a34a;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: var(--accent-color);
            color: white;
        }

        .btn-warning:hover {
            background: #d97706;
            transform: translateY(-2px);
        }

        .alert {
            padding: 15px;
            border-radius: var(--border-radius);
            margin-bottom: 20px;
            border: 1px solid;
        }

        .alert-warning {
            background: #fef3c7;
            border-color: var(--accent-color);
            color: #92400e;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .stats-grid,
            .actions-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏛️ نظام إدارة حراسة الامتحانات</h1>
            <p>نظام شامل لإدارة وتنظيم حراسة الامتحانات الرسمية</p>
        </div>

        <?php if (!$centerSettings): ?>
        <div class="alert alert-warning">
            <strong>تنبيه:</strong> لم يتم إعداد بيانات المركز بعد. يرجى الذهاب لصفحة الإعدادات لإكمال الإعداد.
        </div>
        <?php endif; ?>

        <?php if ($centerSettings): ?>
        <div class="center-info">
            <h3>📋 معلومات المركز</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">اسم المركز:</span>
                    <span class="info-value"><?= htmlspecialchars($centerSettings['center_name']) ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">رمز المركز:</span>
                    <span class="info-value"><?= htmlspecialchars($centerSettings['center_code']) ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">رئيس المركز:</span>
                    <span class="info-value"><?= htmlspecialchars($centerSettings['head_name']) ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">نوع الامتحان:</span>
                    <span class="info-value">
                        <?= $centerSettings['exam_type'] == 'bem' ? 'شهادة التعليم المتوسط' : 'شهادة البكالوريا' ?>
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">عدد القاعات:</span>
                    <span class="info-value"><?= $centerSettings['rooms_count'] ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">عدد المترشحين:</span>
                    <span class="info-value"><?= $centerSettings['candidates_count'] ?></span>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number"><?= $guardsCount ?></div>
                <div class="stat-label">إجمالي الحراس</div>
            </div>
            <div class="stat-card success">
                <div class="stat-number"><?= $schedulesCount ?></div>
                <div class="stat-label">جداول الحراسة</div>
            </div>
            <div class="stat-card warning">
                <div class="stat-number"><?= $centerSettings ? $centerSettings['rooms_count'] : 0 ?></div>
                <div class="stat-label">عدد القاعات</div>
            </div>
            <div class="stat-card info">
                <div class="stat-number"><?= $centerSettings ? $centerSettings['candidates_count'] : 0 ?></div>
                <div class="stat-label">عدد المترشحين</div>
            </div>
        </div>

        <div class="actions-grid">
            <a href="pages/guards/index.php" class="action-card">
                <div class="action-icon">👥</div>
                <div class="action-title">إدارة الحراس</div>
                <div class="action-description">إضافة وتعديل وإدارة قائمة الحراس</div>
            </a>

            <a href="pages/schedules/create.php" class="action-card">
                <div class="action-icon">📅</div>
                <div class="action-title">إنشاء جدول حراسة</div>
                <div class="action-description">إنشاء جدول حراسة جديد للامتحانات</div>
            </a>

            <a href="pages/schedules/index.php" class="action-card">
                <div class="action-icon">📋</div>
                <div class="action-title">عرض الجداول</div>
                <div class="action-description">عرض وإدارة جداول الحراسة المحفوظة</div>
            </a>

            <a href="pages/attendance/index.php" class="action-card">
                <div class="action-icon">✅</div>
                <div class="action-title">تسجيل الحضور</div>
                <div class="action-description">تسجيل حضور وغياب الحراس</div>
            </a>

            <a href="pages/reports/index.php" class="action-card">
                <div class="action-icon">📊</div>
                <div class="action-title">التقارير</div>
                <div class="action-description">عرض التقارير والإحصائيات</div>
            </a>

            <a href="pages/settings/index.php" class="action-card">
                <div class="action-icon">⚙️</div>
                <div class="action-title">الإعدادات</div>
                <div class="action-description">إعدادات المركز والنظام</div>
            </a>
        </div>

        <?php if ($lastSchedule): ?>
        <div style="margin-top: 30px; text-align: center; padding: 20px; background: var(--light-color); border-radius: var(--border-radius);">
            <h4 style="color: var(--primary-color); margin-bottom: 10px;">آخر جدول حراسة</h4>
            <p><strong><?= htmlspecialchars($lastSchedule['schedule_name']) ?></strong></p>
            <p>تاريخ الامتحان: <?= date('Y-m-d', strtotime($lastSchedule['exam_date'])) ?></p>
            <p>الفترة: <?= $lastSchedule['exam_period'] == 'morning' ? 'الصباحية' : 'المسائية' ?></p>
            <a href="pages/schedules/view.php?id=<?= $lastSchedule['id'] ?>" class="btn btn-primary" style="margin-top: 10px;">عرض الجدول</a>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
