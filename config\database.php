<?php
/**
 * إعدادات قاعدة البيانات
 * نظام إدارة حراسة الامتحانات
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'exam_guard_system';
    private $username = 'root';
    private $password = '';
    private $charset = 'utf8mb4';
    public $conn;

    /**
     * الاتصال بقاعدة البيانات
     */
    public function getConnection() {
        $this->conn = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];
            
            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch(PDOException $exception) {
            echo "خطأ في الاتصال بقاعدة البيانات: " . $exception->getMessage();
        }
        
        return $this->conn;
    }

    /**
     * إنشاء قاعدة البيانات والجداول
     */
    public function createDatabase() {
        try {
            // الاتصال بـ MySQL بدون تحديد قاعدة بيانات
            $dsn = "mysql:host=" . $this->host . ";charset=" . $this->charset;
            $pdo = new PDO($dsn, $this->username, $this->password);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // إنشاء قاعدة البيانات
            $sql = "CREATE DATABASE IF NOT EXISTS `{$this->db_name}` 
                    CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            $pdo->exec($sql);
            
            echo "تم إنشاء قاعدة البيانات بنجاح<br>";
            
            // الاتصال بقاعدة البيانات الجديدة
            $this->getConnection();
            
            // إنشاء الجداول
            $this->createTables();
            
        } catch(PDOException $e) {
            echo "خطأ في إنشاء قاعدة البيانات: " . $e->getMessage();
        }
    }

    /**
     * إنشاء الجداول
     */
    private function createTables() {
        $tables = [
            // جدول إعدادات المركز
            'center_settings' => "
                CREATE TABLE IF NOT EXISTS center_settings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    center_name VARCHAR(255) NOT NULL COMMENT 'اسم المركز',
                    center_code VARCHAR(50) NOT NULL COMMENT 'رمز المركز',
                    head_name VARCHAR(255) NOT NULL COMMENT 'اسم رئيس المركز',
                    exam_type ENUM('bem', 'bac') NOT NULL DEFAULT 'bem' COMMENT 'نوع الامتحان',
                    rooms_count INT NOT NULL DEFAULT 14 COMMENT 'عدد القاعات',
                    guards_per_room INT NOT NULL DEFAULT 3 COMMENT 'عدد الحراس لكل قاعة',
                    candidates_count INT NOT NULL DEFAULT 0 COMMENT 'عدد المترشحين',
                    candidates_per_room INT NOT NULL DEFAULT 20 COMMENT 'عدد المترشحين لكل قاعة',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            // جدول الحراس
            'guards' => "
                CREATE TABLE IF NOT EXISTS guards (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    guard_number VARCHAR(50) NOT NULL UNIQUE COMMENT 'رقم الحارس',
                    first_name VARCHAR(100) NOT NULL COMMENT 'الاسم الأول',
                    last_name VARCHAR(100) NOT NULL COMMENT 'اللقب',
                    institution VARCHAR(255) NOT NULL COMMENT 'مؤسسة العمل',
                    subject VARCHAR(100) DEFAULT 'غير محدد' COMMENT 'المادة',
                    phone VARCHAR(20) DEFAULT NULL COMMENT 'رقم الهاتف',
                    email VARCHAR(255) DEFAULT NULL COMMENT 'البريد الإلكتروني',
                    is_active BOOLEAN DEFAULT TRUE COMMENT 'نشط',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_guard_number (guard_number),
                    INDEX idx_institution (institution),
                    INDEX idx_active (is_active)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            // جدول جداول الحراسة
            'guard_schedules' => "
                CREATE TABLE IF NOT EXISTS guard_schedules (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    schedule_name VARCHAR(255) NOT NULL COMMENT 'اسم الجدول',
                    exam_date DATE NOT NULL COMMENT 'تاريخ الامتحان',
                    exam_period ENUM('morning', 'evening') NOT NULL COMMENT 'فترة الامتحان',
                    exam_type ENUM('bem', 'bac') NOT NULL COMMENT 'نوع الامتحان',
                    status ENUM('draft', 'completed', 'published') DEFAULT 'draft' COMMENT 'حالة الجدول',
                    notes TEXT DEFAULT NULL COMMENT 'ملاحظات',
                    created_by VARCHAR(100) DEFAULT NULL COMMENT 'منشئ الجدول',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX idx_exam_date (exam_date),
                    INDEX idx_status (status)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            // جدول تعيينات الحراس
            'guard_assignments' => "
                CREATE TABLE IF NOT EXISTS guard_assignments (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    schedule_id INT NOT NULL COMMENT 'معرف الجدول',
                    guard_id INT NOT NULL COMMENT 'معرف الحارس',
                    room_number VARCHAR(10) NOT NULL COMMENT 'رقم القاعة',
                    guard_type ENUM('main', 'regular') NOT NULL COMMENT 'نوع الحارس',
                    guard_order INT NOT NULL DEFAULT 1 COMMENT 'ترتيب الحارس في القاعة',
                    status ENUM('present', 'absent', 'backup') DEFAULT 'present' COMMENT 'حالة الحارس',
                    notes TEXT DEFAULT NULL COMMENT 'ملاحظات',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    FOREIGN KEY (schedule_id) REFERENCES guard_schedules(id) ON DELETE CASCADE,
                    FOREIGN KEY (guard_id) REFERENCES guards(id) ON DELETE CASCADE,
                    UNIQUE KEY unique_assignment (schedule_id, guard_id),
                    INDEX idx_schedule_room (schedule_id, room_number),
                    INDEX idx_guard_type (guard_type)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ",
            
            // جدول سجل الحضور
            'attendance_log' => "
                CREATE TABLE IF NOT EXISTS attendance_log (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    schedule_id INT NOT NULL COMMENT 'معرف الجدول',
                    guard_id INT NOT NULL COMMENT 'معرف الحارس',
                    attendance_status ENUM('present', 'absent', 'late', 'excused') NOT NULL COMMENT 'حالة الحضور',
                    check_in_time TIMESTAMP NULL COMMENT 'وقت الوصول',
                    check_out_time TIMESTAMP NULL COMMENT 'وقت المغادرة',
                    notes TEXT DEFAULT NULL COMMENT 'ملاحظات',
                    recorded_by VARCHAR(100) DEFAULT NULL COMMENT 'مسجل بواسطة',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (schedule_id) REFERENCES guard_schedules(id) ON DELETE CASCADE,
                    FOREIGN KEY (guard_id) REFERENCES guards(id) ON DELETE CASCADE,
                    INDEX idx_schedule_attendance (schedule_id, attendance_status)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            "
        ];

        foreach ($tables as $tableName => $sql) {
            try {
                $this->conn->exec($sql);
                echo "تم إنشاء جدول {$tableName} بنجاح<br>";
            } catch(PDOException $e) {
                echo "خطأ في إنشاء جدول {$tableName}: " . $e->getMessage() . "<br>";
            }
        }
        
        // إدراج البيانات الافتراضية
        $this->insertDefaultData();
    }

    /**
     * إدراج البيانات الافتراضية
     */
    private function insertDefaultData() {
        try {
            // إعدادات المركز الافتراضية
            $sql = "INSERT IGNORE INTO center_settings 
                    (id, center_name, center_code, head_name, exam_type, rooms_count, guards_per_room, candidates_count, candidates_per_room) 
                    VALUES 
                    (1, 'ثانوية الأخوين كرد - أميه ونسة', '39030', 'مدير المركز', 'bem', 14, 3, 280, 20)";
            $this->conn->exec($sql);
            echo "تم إدراج الإعدادات الافتراضية بنجاح<br>";
            
        } catch(PDOException $e) {
            echo "خطأ في إدراج البيانات الافتراضية: " . $e->getMessage() . "<br>";
        }
    }
}

// إنشاء قاعدة البيانات عند تشغيل هذا الملف مباشرة
if (basename(__FILE__) == basename($_SERVER["SCRIPT_FILENAME"])) {
    $database = new Database();
    echo "<h2>إعداد قاعدة البيانات</h2>";
    $database->createDatabase();
    echo "<br><a href='../index.php'>الذهاب للصفحة الرئيسية</a>";
}
?>
