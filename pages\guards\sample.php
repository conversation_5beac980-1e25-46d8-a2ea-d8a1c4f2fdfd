<?php
/**
 * إنشاء ملف Excel نموذجي للحراس
 */

// مكتبة بسيطة لإنشاء Excel
class SimpleXLSXGen {
    public static function fromArray($data, $filename) {
        // إنشاء XML للـ Excel
        $xml = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>' . "\n";
        $xml .= '<worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main">' . "\n";
        $xml .= '<sheetData>' . "\n";

        $rowIndex = 1;
        foreach ($data as $row) {
            $xml .= '<row r="' . $rowIndex . '">' . "\n";
            $colIndex = 1;
            foreach ($row as $cell) {
                $cellRef = chr(64 + $colIndex) . $rowIndex;
                $xml .= '<c r="' . $cellRef . '" t="inlineStr">';
                $xml .= '<is><t>' . htmlspecialchars($cell) . '</t></is>';
                $xml .= '</c>' . "\n";
                $colIndex++;
            }
            $xml .= '</row>' . "\n";
            $rowIndex++;
        }

        $xml .= '</sheetData>' . "\n";
        $xml .= '</worksheet>';

        // إنشاء ملف ZIP (Excel هو ملف ZIP)
        $zip = new ZipArchive();
        $tempFile = tempnam(sys_get_temp_dir(), 'excel');

        if ($zip->open($tempFile, ZipArchive::CREATE) === TRUE) {
            // إضافة الملفات المطلوبة
            $zip->addFromString('[Content_Types].xml', self::getContentTypes());
            $zip->addFromString('_rels/.rels', self::getRels());
            $zip->addFromString('xl/_rels/workbook.xml.rels', self::getWorkbookRels());
            $zip->addFromString('xl/workbook.xml', self::getWorkbook());
            $zip->addFromString('xl/worksheets/sheet1.xml', $xml);
            $zip->addFromString('xl/sharedStrings.xml', self::getSharedStrings());
            $zip->addFromString('xl/styles.xml', self::getStyles());

            $zip->close();

            // إرسال الملف للمتصفح
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            header('Content-Length: ' . filesize($tempFile));

            readfile($tempFile);
            unlink($tempFile);
            exit();
        }
    }

    private static function getContentTypes() {
        return '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
<Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
<Default Extension="xml" ContentType="application/xml"/>
<Override PartName="/xl/workbook.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml"/>
<Override PartName="/xl/worksheets/sheet1.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml"/>
<Override PartName="/xl/sharedStrings.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml"/>
<Override PartName="/xl/styles.xml" ContentType="application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml"/>
</Types>';
    }

    private static function getRels() {
        return '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
<Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="xl/workbook.xml"/>
</Relationships>';
    }

    private static function getWorkbookRels() {
        return '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
<Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet" Target="worksheets/sheet1.xml"/>
<Relationship Id="rId2" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings" Target="sharedStrings.xml"/>
<Relationship Id="rId3" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles" Target="styles.xml"/>
</Relationships>';
    }

    private static function getWorkbook() {
        return '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<workbook xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main">
<sheets>
<sheet name="الحراس" sheetId="1" r:id="rId1" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships"/>
</sheets>
</workbook>';
    }

    private static function getSharedStrings() {
        return '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<sst xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main">
</sst>';
    }

    private static function getStyles() {
        return '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<styleSheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main">
<fonts count="1">
<font>
<sz val="11"/>
<name val="Calibri"/>
</font>
</fonts>
<fills count="1">
<fill>
<patternFill patternType="none"/>
</fill>
</fills>
<borders count="1">
<border>
<left/>
<right/>
<top/>
<bottom/>
<diagonal/>
</border>
</borders>
<cellStyleXfs count="1">
<xf numFmtId="0" fontId="0" fillId="0" borderId="0"/>
</cellStyleXfs>
<cellXfs count="1">
<xf numFmtId="0" fontId="0" fillId="0" borderId="0" xfId="0"/>
</cellXfs>
</styleSheet>';
    }
}

// البيانات النموذجية
$data = [
    ['الرقم', 'الإسم', 'اللقب', 'مؤسسة العمل', 'المادة', 'الهاتف', 'البريد الإلكتروني'],
    ['001', 'أحمد', 'بن علي', 'ثانوية الأخوين كرد', 'الرياضيات', '0555123456', '<EMAIL>'],
    ['002', 'فاطمة', 'بن محمد', 'متوسطة النور', 'اللغة العربية', '0555234567', '<EMAIL>'],
    ['003', 'محمد', 'بن أحمد', 'ثانوية الشهيد', 'الفيزياء', '0555345678', '<EMAIL>'],
    ['004', 'خديجة', 'بن يوسف', 'متوسطة السلام', 'التاريخ والجغرافيا', '0555456789', '<EMAIL>'],
    ['005', 'عبد الله', 'بن سالم', 'ثانوية المستقبل', 'الكيمياء', '0555567890', '<EMAIL>'],
    ['006', 'عائشة', 'بن عمر', 'متوسطة الأمل', 'الإنجليزية', '0555678901', '<EMAIL>'],
    ['007', 'يوسف', 'بن إبراهيم', 'ثانوية التقدم', 'علوم الطبيعة', '0555789012', '<EMAIL>'],
    ['008', 'زينب', 'بن حسن', 'متوسطة الفجر', 'الفرنسية', '0555890123', '<EMAIL>'],
    ['009', 'عمر', 'بن خالد', 'ثانوية النجاح', 'التربية الإسلامية', '0555901234', '<EMAIL>'],
    ['010', 'مريم', 'بن عبد الرحمن', 'متوسطة الهدى', 'التربية المدنية', '0556012345', '<EMAIL>']
];

// إنشاء وتحميل الملف
SimpleXLSXGen::fromArray($data, 'نموذج_الحراس.xlsx');
?>
