<?php
/**
 * صفحة نسخ جدول الحراسة
 */

require_once '../../config/database.php';

$database = new Database();
$conn = $database->getConnection();

$originalId = $_GET['id'] ?? null;

if (!$originalId) {
    header('Location: index.php');
    exit();
}

// جلب بيانات الجدول الأصلي
try {
    $stmt = $conn->prepare("SELECT * FROM guard_schedules WHERE id = ?");
    $stmt->execute([$originalId]);
    $originalSchedule = $stmt->fetch();
    
    if (!$originalSchedule) {
        header('Location: index.php');
        exit();
    }
} catch(PDOException $e) {
    die('خطأ في جلب بيانات الجدول: ' . $e->getMessage());
}

// جلب التعيينات الأصلية
try {
    $stmt = $conn->prepare("SELECT * FROM guard_assignments WHERE schedule_id = ?");
    $stmt->execute([$originalId]);
    $originalAssignments = $stmt->fetchAll();
} catch(PDOException $e) {
    die('خطأ في جلب التعيينات: ' . $e->getMessage());
}

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $scheduleName = $_POST['schedule_name'] ?? '';
    $examDate = $_POST['exam_date'] ?? '';
    $examPeriod = $_POST['exam_period'] ?? '';
    $examType = $_POST['exam_type'] ?? '';
    $notes = $_POST['notes'] ?? '';
    $copyAssignments = isset($_POST['copy_assignments']);
    
    if (empty($scheduleName) || empty($examDate) || empty($examPeriod) || empty($examType)) {
        $error = 'جميع الحقول مطلوبة';
    } else {
        try {
            $conn->beginTransaction();
            
            // إنشاء الجدول الجديد
            $stmt = $conn->prepare("
                INSERT INTO guard_schedules (schedule_name, exam_date, exam_period, exam_type, notes, status, created_at, updated_at) 
                VALUES (?, ?, ?, ?, ?, 'draft', NOW(), NOW())
            ");
            $stmt->execute([$scheduleName, $examDate, $examPeriod, $examType, $notes]);
            $newScheduleId = $conn->lastInsertId();
            
            // نسخ التعيينات إذا كان مطلوباً
            if ($copyAssignments && !empty($originalAssignments)) {
                $stmt = $conn->prepare("
                    INSERT INTO guard_assignments (schedule_id, guard_id, room_number, guard_type, guard_order, created_at, updated_at) 
                    VALUES (?, ?, ?, ?, ?, NOW(), NOW())
                ");
                
                foreach ($originalAssignments as $assignment) {
                    $stmt->execute([
                        $newScheduleId,
                        $assignment['guard_id'],
                        $assignment['room_number'],
                        $assignment['guard_type'],
                        $assignment['guard_order']
                    ]);
                }
            }
            
            $conn->commit();
            header("Location: view.php?id=$newScheduleId&copied=1");
            exit();
            
        } catch(PDOException $e) {
            $conn->rollBack();
            $error = 'خطأ في نسخ الجدول: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نسخ جدول الحراسة</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../../assets/css/style.css">
</head>
<body>
    <div class="container">
        <div class="page-header">
            <h1>📋 نسخ جدول الحراسة</h1>
            <p>إنشاء نسخة جديدة من الجدول مع إمكانية التعديل</p>
            <a href="view.php?id=<?= $originalId ?>" class="btn btn-secondary">← العودة للجدول الأصلي</a>
        </div>

        <?php if (isset($error)): ?>
        <div class="alert alert-error">
            <strong>خطأ!</strong> <?= htmlspecialchars($error) ?>
        </div>
        <?php endif; ?>

        <div class="form-container">
            <div class="original-info">
                <h3>📄 معلومات الجدول الأصلي</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="label">اسم الجدول:</span>
                        <span class="value"><?= htmlspecialchars($originalSchedule['schedule_name']) ?></span>
                    </div>
                    <div class="info-item">
                        <span class="label">تاريخ الامتحان:</span>
                        <span class="value"><?= date('Y-m-d', strtotime($originalSchedule['exam_date'])) ?></span>
                    </div>
                    <div class="info-item">
                        <span class="label">فترة الامتحان:</span>
                        <span class="value"><?= $originalSchedule['exam_period'] == 'morning' ? 'الصباحية' : 'المسائية' ?></span>
                    </div>
                    <div class="info-item">
                        <span class="label">نوع الامتحان:</span>
                        <span class="value"><?= $originalSchedule['exam_type'] == 'bem' ? 'شهادة التعليم المتوسط' : 'شهادة البكالوريا' ?></span>
                    </div>
                </div>
            </div>

            <form method="POST" class="schedule-form">
                <h3>✏️ بيانات النسخة الجديدة</h3>
                
                <div class="form-group">
                    <label for="schedule_name">اسم الجدول الجديد</label>
                    <input type="text" id="schedule_name" name="schedule_name" 
                           value="نسخة من <?= htmlspecialchars($originalSchedule['schedule_name']) ?>" 
                           required>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="exam_date">تاريخ الامتحان</label>
                        <input type="date" id="exam_date" name="exam_date" 
                               value="<?= $originalSchedule['exam_date'] ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="exam_period">فترة الامتحان</label>
                        <select id="exam_period" name="exam_period" required>
                            <option value="morning" <?= $originalSchedule['exam_period'] == 'morning' ? 'selected' : '' ?>>الصباحية</option>
                            <option value="evening" <?= $originalSchedule['exam_period'] == 'evening' ? 'selected' : '' ?>>المسائية</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label for="exam_type">نوع الامتحان</label>
                    <select id="exam_type" name="exam_type" required>
                        <option value="bem" <?= $originalSchedule['exam_type'] == 'bem' ? 'selected' : '' ?>>شهادة التعليم المتوسط</option>
                        <option value="bac" <?= $originalSchedule['exam_type'] == 'bac' ? 'selected' : '' ?>>شهادة البكالوريا</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="notes">ملاحظات</label>
                    <textarea id="notes" name="notes" rows="4"><?= htmlspecialchars($originalSchedule['notes']) ?></textarea>
                </div>

                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="copy_assignments" checked>
                        <span class="checkmark"></span>
                        نسخ تعيينات الحراس من الجدول الأصلي
                        <small>إذا تم إلغاء التحديد، سيتم إنشاء جدول فارغ</small>
                    </label>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">📋 إنشاء النسخة</button>
                    <a href="view.php?id=<?= $originalId ?>" class="btn btn-secondary">إلغاء</a>
                </div>
            </form>
        </div>
    </div>

    <style>
        .original-info {
            background: var(--light-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 30px;
        }

        .original-info h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
        }

        .info-item .label {
            font-weight: 600;
            color: var(--dark-color);
        }

        .info-item .value {
            color: var(--primary-color);
            font-weight: 500;
        }

        .checkbox-label {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            cursor: pointer;
            padding: 15px;
            background: #f8fafc;
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .checkbox-label:hover {
            background: #e2e8f0;
        }

        .checkbox-label input[type="checkbox"] {
            margin: 0;
        }

        .checkbox-label small {
            display: block;
            color: #666;
            font-size: 0.85rem;
            margin-top: 5px;
        }
    </style>
</body>
</html>
