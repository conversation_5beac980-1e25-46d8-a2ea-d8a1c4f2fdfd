/**
 * ملف التنسيقات المشتركة
 * نظام إدارة حراسة الامتحانات
 */

:root {
    --primary-color: #2563eb;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    --danger-color: #ef4444;
    --success-color: #22c55e;
    --info-color: #06b6d4;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --border-color: #e2e8f0;
    --shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --border-radius: 12px;
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    min-height: 100vh;
    padding: 20px;
    direction: rtl;
    line-height: 1.6;
    color: var(--dark-color);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: var(--border-radius);
    padding: 30px;
    box-shadow: var(--shadow-medium);
}

.header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid var(--border-color);
    position: relative;
}

.header h1 {
    font-size: 2.2rem;
    font-weight: 800;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.header p {
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 15px;
}

.header .btn {
    position: absolute;
    top: 0;
    right: 0;
}

/* الأزرار */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    text-align: center;
    font-size: 1rem;
    line-height: 1;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #1d4ed8;
}

.btn-secondary {
    background: var(--border-color);
    color: var(--dark-color);
}

.btn-secondary:hover {
    background: #cbd5e1;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-success:hover {
    background: #16a34a;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
}

.btn-warning {
    background: var(--accent-color);
    color: white;
}

.btn-warning:hover {
    background: #d97706;
}

.btn-info {
    background: var(--info-color);
    color: white;
}

.btn-info:hover {
    background: #0891b2;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 0.9rem;
}

.btn-lg {
    padding: 16px 32px;
    font-size: 1.1rem;
}

/* النماذج */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--dark-color);
}

.form-control {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-control:invalid {
    border-color: var(--danger-color);
}

/* الجداول */
.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.table th,
.table td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background: var(--light-color);
    font-weight: 600;
    color: var(--dark-color);
}

.table tr:hover {
    background: #f8fafc;
}

.table-striped tbody tr:nth-child(odd) {
    background: #f8fafc;
}

/* البطاقات */
.card {
    background: white;
    border-radius: var(--border-radius);
    padding: 25px;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.card-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.card-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 5px;
}

.card-subtitle {
    color: #666;
    font-size: 0.95rem;
}

/* التنبيهات */
.alert {
    padding: 15px;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    border: 1px solid;
}

.alert-success {
    background: #d1fae5;
    border-color: var(--success-color);
    color: #065f46;
}

.alert-error {
    background: #fee2e2;
    border-color: var(--danger-color);
    color: #991b1b;
}

.alert-warning {
    background: #fef3c7;
    border-color: var(--accent-color);
    color: #92400e;
}

.alert-info {
    background: #e0f2fe;
    border-color: var(--info-color);
    color: #0c4a6e;
}

/* الشبكات */
.grid {
    display: grid;
    gap: 20px;
}

.grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* المرافق */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }
.mb-5 { margin-bottom: 2.5rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
.mt-5 { margin-top: 2.5rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }
.p-4 { padding: 2rem; }
.p-5 { padding: 2.5rem; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.align-center { align-items: center; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }

.gap-1 { gap: 0.5rem; }
.gap-2 { gap: 1rem; }
.gap-3 { gap: 1.5rem; }
.gap-4 { gap: 2rem; }
.gap-5 { gap: 2.5rem; }

/* الاستجابة */
@media (max-width: 768px) {
    .container {
        padding: 20px;
        margin: 10px;
    }

    .header h1 {
        font-size: 1.8rem;
    }

    .header .btn {
        position: static;
        margin-top: 15px;
    }

    .grid-2,
    .grid-3,
    .grid-4 {
        grid-template-columns: 1fr;
    }

    .btn {
        width: 100%;
        margin-bottom: 10px;
    }

    .table {
        font-size: 0.9rem;
    }

    .table th,
    .table td {
        padding: 8px;
    }
}

@media (max-width: 480px) {
    body {
        padding: 10px;
    }

    .container {
        padding: 15px;
    }

    .header h1 {
        font-size: 1.5rem;
    }

    .btn {
        padding: 10px 16px;
        font-size: 0.9rem;
    }
}
