<?php
/**
 * صفحة عرض جدول الحراسة
 */

require_once '../../config/database.php';

$database = new Database();
$conn = $database->getConnection();

$scheduleId = $_GET['id'] ?? null;

if (!$scheduleId) {
    header('Location: index.php');
    exit();
}

// جلب بيانات الجدول
try {
    $stmt = $conn->prepare("SELECT * FROM guard_schedules WHERE id = ?");
    $stmt->execute([$scheduleId]);
    $schedule = $stmt->fetch();
    
    if (!$schedule) {
        header('Location: index.php');
        exit();
    }
} catch(PDOException $e) {
    die('خطأ في جلب بيانات الجدول: ' . $e->getMessage());
}

// جلب إعدادات المركز
$stmt = $conn->prepare("SELECT * FROM center_settings WHERE id = 1");
$stmt->execute();
$centerSettings = $stmt->fetch();

// جلب التعيينات مع معلومات الحراس
$stmt = $conn->prepare("
    SELECT ga.*, g.guard_number, g.first_name, g.last_name, g.institution
    FROM guard_assignments ga
    JOIN guards g ON ga.guard_id = g.id
    WHERE ga.schedule_id = ?
    ORDER BY ga.room_number, ga.guard_order
");
$stmt->execute([$scheduleId]);
$assignments = $stmt->fetchAll();

// تنظيم التعيينات حسب القاعة
$roomAssignments = [];
foreach ($assignments as $assignment) {
    $roomAssignments[$assignment['room_number']][] = $assignment;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض جدول الحراسة - نظام إدارة حراسة الامتحانات</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .schedule-header {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            color: white;
            padding: 30px;
            border-radius: var(--border-radius);
            margin-bottom: 30px;
            text-align: center;
        }

        .schedule-title {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 15px;
        }

        .schedule-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .info-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }

        .info-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .info-value {
            font-size: 1.1rem;
            font-weight: 600;
        }

        .guards-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--shadow-light);
            margin-bottom: 30px;
        }

        .guards-table th,
        .guards-table td {
            padding: 15px;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
        }

        .guards-table th {
            background: var(--primary-color);
            color: white;
            font-weight: 600;
        }

        .guards-table tr:hover {
            background: #f8fafc;
        }

        .room-number {
            font-weight: 800;
            color: var(--primary-color);
            font-size: 1.1rem;
        }

        .main-guard {
            background: #fef2f2;
            color: var(--danger-color);
            font-weight: 700;
            border-radius: 6px;
            padding: 8px;
        }

        .regular-guard {
            background: #f0f9ff;
            color: var(--primary-color);
            font-weight: 600;
            border-radius: 6px;
            padding: 8px;
        }

        .guard-info {
            display: flex;
            flex-direction: column;
            gap: 3px;
        }

        .guard-name {
            font-weight: 600;
        }

        .guard-number {
            font-size: 0.8rem;
            color: #666;
        }

        .guard-institution {
            font-size: 0.75rem;
            color: #888;
        }

        .empty-cell {
            color: #999;
            font-style: italic;
        }

        .actions-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .stats-summary {
            background: var(--light-color);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }

        @media print {
            .actions-bar,
            .header {
                display: none;
            }
            
            body {
                background: white;
                padding: 0;
            }
            
            .container {
                background: white;
                box-shadow: none;
                padding: 20px;
            }
        }

        @media (max-width: 768px) {
            .guards-table {
                font-size: 0.8rem;
            }
            
            .guards-table th,
            .guards-table td {
                padding: 8px;
            }
            
            .schedule-info {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👁️ عرض جدول الحراسة</h1>
            <p>عرض تفاصيل وتعيينات جدول الحراسة</p>
            <a href="index.php" class="btn btn-secondary">← العودة لقائمة الجداول</a>
        </div>

        <div class="actions-bar">
            <div>
                <a href="edit.php?id=<?= $scheduleId ?>" class="btn btn-warning">✏️ تعديل الجدول</a>
                <a href="copy.php?id=<?= $scheduleId ?>" class="btn btn-success">📋 نسخ الجدول</a>
                <a href="print.php?id=<?= $scheduleId ?>" class="btn btn-info" target="_blank">🖨️ طباعة</a>
            </div>
            
            <div>
                <span class="status-badge status-<?= $schedule['status'] ?>">
                    <?php
                    $statusLabels = [
                        'draft' => 'مسودة',
                        'completed' => 'مكتمل',
                        'published' => 'منشور'
                    ];
                    echo $statusLabels[$schedule['status']] ?? $schedule['status'];
                    ?>
                </span>
            </div>
        </div>

        <div class="schedule-header">
            <div class="schedule-title"><?= htmlspecialchars($schedule['schedule_name']) ?></div>
            
            <div class="schedule-info">
                <div class="info-card">
                    <div class="info-label">تاريخ الامتحان</div>
                    <div class="info-value"><?= date('Y-m-d', strtotime($schedule['exam_date'])) ?></div>
                </div>
                <div class="info-card">
                    <div class="info-label">فترة الامتحان</div>
                    <div class="info-value"><?= $schedule['exam_period'] == 'morning' ? 'الصباحية' : 'المسائية' ?></div>
                </div>
                <div class="info-card">
                    <div class="info-label">نوع الامتحان</div>
                    <div class="info-value">
                        <?= $schedule['exam_type'] == 'bem' ? 'شهادة التعليم المتوسط' : 'شهادة البكالوريا' ?>
                    </div>
                </div>
                <div class="info-card">
                    <div class="info-label">تاريخ الإنشاء</div>
                    <div class="info-value"><?= date('Y-m-d', strtotime($schedule['created_at'])) ?></div>
                </div>
            </div>
        </div>

        <?php
        // حساب الإحصائيات
        $totalAssignments = count($assignments);
        $mainGuardsCount = count(array_filter($assignments, function($a) { return $a['guard_type'] == 'main'; }));
        $regularGuardsCount = count(array_filter($assignments, function($a) { return $a['guard_type'] == 'regular'; }));
        $roomsWithGuards = count($roomAssignments);
        ?>

        <div class="stats-summary">
            <h3 style="margin-bottom: 15px; color: var(--primary-color);">📊 ملخص الإحصائيات</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number"><?= $totalAssignments ?></div>
                    <div class="stat-label">إجمالي التعيينات</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= $mainGuardsCount ?></div>
                    <div class="stat-label">حراس رئيسيين</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= $regularGuardsCount ?></div>
                    <div class="stat-label">حراس عاديين</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><?= $roomsWithGuards ?></div>
                    <div class="stat-label">قاعات مُعيَّنة</div>
                </div>
            </div>
        </div>

        <table class="guards-table">
            <thead>
                <tr>
                    <th>رقم القاعة</th>
                    <th>الحارس الرئيسي</th>
                    <th>الحارس العادي الأول</th>
                    <th>الحارس العادي الثاني</th>
                </tr>
            </thead>
            <tbody>
                <?php for ($room = 1; $room <= ($centerSettings['rooms_count'] ?? 14); $room++): ?>
                <?php 
                $roomNumber = str_pad($room, 2, '0', STR_PAD_LEFT);
                $roomGuards = $roomAssignments[$roomNumber] ?? [];
                
                $mainGuard = null;
                $guard2 = null;
                $guard3 = null;
                
                foreach ($roomGuards as $guard) {
                    if ($guard['guard_type'] == 'main') {
                        $mainGuard = $guard;
                    } elseif ($guard['guard_type'] == 'regular') {
                        if ($guard['guard_order'] == 2) $guard2 = $guard;
                        if ($guard['guard_order'] == 3) $guard3 = $guard;
                    }
                }
                ?>
                <tr>
                    <td class="room-number"><?= $roomNumber ?></td>
                    
                    <td>
                        <?php if ($mainGuard): ?>
                        <div class="main-guard">
                            <div class="guard-info">
                                <div class="guard-name"><?= htmlspecialchars($mainGuard['first_name'] . ' ' . $mainGuard['last_name']) ?></div>
                                <div class="guard-number">رقم: <?= htmlspecialchars($mainGuard['guard_number']) ?></div>
                                <div class="guard-institution"><?= htmlspecialchars($mainGuard['institution']) ?></div>
                            </div>
                        </div>
                        <?php else: ?>
                        <div class="empty-cell">غير معيَّن</div>
                        <?php endif; ?>
                    </td>
                    
                    <td>
                        <?php if ($guard2): ?>
                        <div class="regular-guard">
                            <div class="guard-info">
                                <div class="guard-name"><?= htmlspecialchars($guard2['first_name'] . ' ' . $guard2['last_name']) ?></div>
                                <div class="guard-number">رقم: <?= htmlspecialchars($guard2['guard_number']) ?></div>
                                <div class="guard-institution"><?= htmlspecialchars($guard2['institution']) ?></div>
                            </div>
                        </div>
                        <?php else: ?>
                        <div class="empty-cell">غير معيَّن</div>
                        <?php endif; ?>
                    </td>
                    
                    <td>
                        <?php if ($guard3): ?>
                        <div class="regular-guard">
                            <div class="guard-info">
                                <div class="guard-name"><?= htmlspecialchars($guard3['first_name'] . ' ' . $guard3['last_name']) ?></div>
                                <div class="guard-number">رقم: <?= htmlspecialchars($guard3['guard_number']) ?></div>
                                <div class="guard-institution"><?= htmlspecialchars($guard3['institution']) ?></div>
                            </div>
                        </div>
                        <?php else: ?>
                        <div class="empty-cell">غير معيَّن</div>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endfor; ?>
            </tbody>
        </table>

        <?php if ($schedule['notes']): ?>
        <div style="background: #fef3c7; border: 1px solid var(--accent-color); border-radius: var(--border-radius); padding: 20px; margin-top: 20px;">
            <h4 style="color: #92400e; margin-bottom: 10px;">📝 ملاحظات:</h4>
            <p style="color: #92400e; margin: 0;"><?= nl2br(htmlspecialchars($schedule['notes'])) ?></p>
        </div>
        <?php endif; ?>

        <div style="text-align: center; margin-top: 30px; padding: 20px; background: var(--light-color); border-radius: var(--border-radius);">
            <p style="color: #666; margin-bottom: 15px;">آخر تحديث: <?= date('Y-m-d H:i:s', strtotime($schedule['updated_at'])) ?></p>
            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                <a href="edit.php?id=<?= $scheduleId ?>" class="btn btn-warning">✏️ تعديل الجدول</a>
                <a href="copy.php?id=<?= $scheduleId ?>" class="btn btn-success">📋 نسخ الجدول</a>
                <a href="print.php?id=<?= $scheduleId ?>" class="btn btn-info" target="_blank">🖨️ طباعة</a>
                <a href="index.php" class="btn btn-secondary">📋 عرض جميع الجداول</a>
            </div>
        </div>
    </div>
</body>
</html>
