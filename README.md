# نظام إدارة حراسة الامتحانات

نظام شامل لإدارة وتنظيم حراسة الامتحانات الرسمية باستخدام PHP و MySQL.

## 🌟 المميزات

### 📋 إدارة الحراس
- إضافة وتعديل وحذف الحراس
- استيراد قوائم الحراس من ملفات Excel
- البحث والفلترة المتقدمة
- تتبع معلومات الحراس (الاسم، المؤسسة، المادة، الهاتف)

### 📅 إدارة جداول الحراسة
- إنشاء جداول حراسة جديدة
- تعيين الحراس للقاعات (حارس رئيسي + حراس عاديين)
- عرض وطباعة الجداول
- حفظ وتحديث الجداول

### ✅ تسجيل الحضور
- تسجيل حضور وغياب الحراس
- تتبع أوقات الوصول والمغادرة
- إدارة الحالات (حاضر، غائب، متأخر، معذور)

### 📊 التقارير والإحصائيات
- تقارير شاملة عن الحضور
- إحصائيات الحراس والجداول
- تصدير التقارير بصيغ مختلفة

### ⚙️ الإعدادات
- إعدادات المركز والامتحانات
- إدارة قاعدة البيانات
- النسخ الاحتياطي والاستعادة

## 🛠️ متطلبات النظام

- **PHP**: 7.4 أو أحدث
- **MySQL**: 5.7 أو أحدث
- **Apache/Nginx**: خادم ويب
- **Composer**: لإدارة المكتبات

### المكتبات المطلوبة
- `phpoffice/phpspreadsheet`: لقراءة ملفات Excel
- `dompdf/dompdf`: لإنشاء ملفات PDF
- `tecnickcom/tcpdf`: لإنشاء التقارير

## 📦 التثبيت

### 1. تحميل المشروع
```bash
git clone https://github.com/your-repo/exam-guard-system.git
cd exam-guard-system
```

### 2. تثبيت المكتبات
```bash
composer install
```

### 3. إعداد قاعدة البيانات
1. أنشئ قاعدة بيانات MySQL جديدة
2. عدّل إعدادات الاتصال في `config/database.php`:
```php
private $host = 'localhost';
private $db_name = 'exam_guard_system';
private $username = 'your_username';
private $password = 'your_password';
```

### 4. إنشاء الجداول
```bash
php config/database.php
```
أو زر الرابط: `http://your-domain/config/database.php`

### 5. إعداد الخادم
- تأكد من أن مجلد المشروع في مجلد الخادم (htdocs/www)
- تأكد من تفعيل امتدادات PHP المطلوبة:
  - `pdo`
  - `pdo_mysql`
  - `zip`
  - `xml`

## 🚀 الاستخدام

### الوصول للنظام
افتح المتصفح وانتقل إلى: `http://your-domain/`

### الخطوات الأولى
1. **إعداد المركز**: انتقل لصفحة الإعدادات وأدخل بيانات المركز
2. **إضافة الحراس**: استخدم صفحة إدارة الحراس لإضافة قائمة الحراس
3. **إنشاء جدول**: أنشئ جدول حراسة جديد وعيّن الحراس
4. **طباعة الجدول**: اطبع الجدول النهائي للاستخدام

## 📁 هيكل المشروع

```
exam-guard-system/
├── config/
│   └── database.php          # إعدادات قاعدة البيانات
├── pages/
│   ├── guards/              # صفحات إدارة الحراس
│   │   ├── index.php        # قائمة الحراس
│   │   └── import.php       # استيراد Excel
│   ├── schedules/           # صفحات إدارة الجداول
│   │   ├── index.php        # قائمة الجداول
│   │   ├── create.php       # إنشاء جدول جديد
│   │   ├── view.php         # عرض الجدول
│   │   └── print.php        # طباعة الجدول
│   ├── attendance/          # صفحات تسجيل الحضور
│   ├── reports/             # صفحات التقارير
│   └── settings/            # صفحات الإعدادات
├── assets/
│   └── css/
│       └── style.css        # ملف التنسيقات
├── vendor/                  # مكتبات Composer
├── index.php               # الصفحة الرئيسية
├── composer.json           # إعدادات Composer
└── README.md              # هذا الملف
```

## 🗄️ قاعدة البيانات

### الجداول الرئيسية

#### `center_settings`
إعدادات المركز والامتحانات

#### `guards`
معلومات الحراس
- `id`: المعرف الفريد
- `guard_number`: رقم الحارس
- `first_name`: الاسم الأول
- `last_name`: اللقب
- `institution`: مؤسسة العمل
- `subject`: المادة
- `phone`: رقم الهاتف
- `email`: البريد الإلكتروني
- `is_active`: حالة النشاط

#### `guard_schedules`
جداول الحراسة
- `id`: المعرف الفريد
- `schedule_name`: اسم الجدول
- `exam_date`: تاريخ الامتحان
- `exam_period`: فترة الامتحان
- `exam_type`: نوع الامتحان
- `status`: حالة الجدول

#### `guard_assignments`
تعيينات الحراس
- `id`: المعرف الفريد
- `schedule_id`: معرف الجدول
- `guard_id`: معرف الحارس
- `room_number`: رقم القاعة
- `guard_type`: نوع الحارس (رئيسي/عادي)
- `guard_order`: ترتيب الحارس
- `status`: حالة الحارس

#### `attendance_log`
سجل الحضور
- `id`: المعرف الفريد
- `schedule_id`: معرف الجدول
- `guard_id`: معرف الحارس
- `attendance_status`: حالة الحضور
- `check_in_time`: وقت الوصول
- `check_out_time`: وقت المغادرة

## 🎨 التخصيص

### تغيير الألوان
عدّل المتغيرات في `assets/css/style.css`:
```css
:root {
    --primary-color: #2563eb;
    --secondary-color: #10b981;
    --accent-color: #f59e0b;
    /* ... */
}
```

### إضافة حقول جديدة
1. عدّل جدول قاعدة البيانات
2. حدّث النماذج في الصفحات المناسبة
3. عدّل استعلامات قاعدة البيانات

## 🔧 استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في الاتصال بقاعدة البيانات
- تأكد من صحة بيانات الاتصال في `config/database.php`
- تأكد من تشغيل خادم MySQL
- تأكد من وجود قاعدة البيانات

#### خطأ في رفع ملفات Excel
- تأكد من تثبيت مكتبة PhpSpreadsheet
- تأكد من صلاحيات الكتابة في مجلد المشروع
- تحقق من حجم الملف المرفوع

#### مشاكل في العرض
- تأكد من تحميل ملف CSS
- تحقق من إعدادات المتصفح
- امسح ذاكرة التخزين المؤقت

## 📞 الدعم

للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من سجلات الأخطاء في PHP
3. تأكد من متطلبات النظام

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للتفاصيل.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## 📝 سجل التغييرات

### الإصدار 1.0.0
- إطلاق النسخة الأولى
- إدارة الحراس والجداول
- تسجيل الحضور
- التقارير الأساسية
- واجهة عربية كاملة

---

**تم تطوير هذا النظام لخدمة المؤسسات التعليمية في تنظيم امتحاناتها بكفاءة عالية.**
