<?php
/**
 * صفحة تعديل جدول الحراسة
 */

require_once '../../config/database.php';

$database = new Database();
$conn = $database->getConnection();

$message = '';
$messageType = '';
$scheduleId = $_GET['id'] ?? null;

if (!$scheduleId) {
    header('Location: index.php');
    exit();
}

// جلب بيانات الجدول
try {
    $stmt = $conn->prepare("SELECT * FROM guard_schedules WHERE id = ?");
    $stmt->execute([$scheduleId]);
    $schedule = $stmt->fetch();
    
    if (!$schedule) {
        header('Location: index.php');
        exit();
    }
} catch(PDOException $e) {
    $message = 'خطأ في جلب بيانات الجدول: ' . $e->getMessage();
    $messageType = 'error';
}

// جلب إعدادات المركز
$stmt = $conn->prepare("SELECT * FROM center_settings WHERE id = 1");
$stmt->execute();
$centerSettings = $stmt->fetch();

// جلب قائمة الحراس النشطين
$stmt = $conn->prepare("SELECT * FROM guards WHERE is_active = 1 ORDER BY guard_number ASC");
$stmt->execute();
$guards = $stmt->fetchAll();

// جلب التعيينات الحالية
$stmt = $conn->prepare("SELECT * FROM guard_assignments WHERE schedule_id = ? ORDER BY room_number, guard_order");
$stmt->execute([$scheduleId]);
$assignments = $stmt->fetchAll();

// تنظيم التعيينات حسب القاعة
$roomAssignments = [];
foreach ($assignments as $assignment) {
    $roomAssignments[$assignment['room_number']][$assignment['guard_type']][] = $assignment;
}

// معالجة التحديث
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_basic_info':
                try {
                    $stmt = $conn->prepare("
                        UPDATE guard_schedules 
                        SET schedule_name = ?, exam_date = ?, exam_period = ?, exam_type = ?, notes = ?, updated_at = NOW()
                        WHERE id = ?
                    ");
                    $stmt->execute([
                        $_POST['schedule_name'],
                        $_POST['exam_date'],
                        $_POST['exam_period'],
                        $_POST['exam_type'],
                        $_POST['notes'] ?? '',
                        $scheduleId
                    ]);
                    
                    $message = 'تم تحديث معلومات الجدول بنجاح';
                    $messageType = 'success';
                    
                    // إعادة جلب البيانات المحدثة
                    $stmt = $conn->prepare("SELECT * FROM guard_schedules WHERE id = ?");
                    $stmt->execute([$scheduleId]);
                    $schedule = $stmt->fetch();
                    
                } catch(PDOException $e) {
                    $message = 'خطأ في تحديث الجدول: ' . $e->getMessage();
                    $messageType = 'error';
                }
                break;
                
            case 'update_assignments':
                try {
                    $conn->beginTransaction();
                    
                    // حذف التعيينات السابقة
                    $stmt = $conn->prepare("DELETE FROM guard_assignments WHERE schedule_id = ?");
                    $stmt->execute([$scheduleId]);
                    
                    // إضافة التعيينات الجديدة
                    $stmt = $conn->prepare("
                        INSERT INTO guard_assignments (schedule_id, guard_id, room_number, guard_type, guard_order, status) 
                        VALUES (?, ?, ?, ?, ?, 'present')
                    ");
                    
                    $roomsCount = $centerSettings['rooms_count'];
                    
                    for ($room = 1; $room <= $roomsCount; $room++) {
                        $roomNumber = str_pad($room, 2, '0', STR_PAD_LEFT);
                        
                        // الحارس الرئيسي
                        if (!empty($_POST["main_guard_$room"])) {
                            $stmt->execute([$scheduleId, $_POST["main_guard_$room"], $roomNumber, 'main', 1]);
                        }
                        
                        // الحراس العاديين
                        if (!empty($_POST["guard2_$room"])) {
                            $stmt->execute([$scheduleId, $_POST["guard2_$room"], $roomNumber, 'regular', 2]);
                        }
                        
                        if (!empty($_POST["guard3_$room"])) {
                            $stmt->execute([$scheduleId, $_POST["guard3_$room"], $roomNumber, 'regular', 3]);
                        }
                    }
                    
                    $conn->commit();
                    
                    $message = 'تم تحديث تعيينات الحراس بنجاح!';
                    $messageType = 'success';
                    
                    // إعادة جلب التعيينات المحدثة
                    $stmt = $conn->prepare("SELECT * FROM guard_assignments WHERE schedule_id = ? ORDER BY room_number, guard_order");
                    $stmt->execute([$scheduleId]);
                    $assignments = $stmt->fetchAll();
                    
                    // إعادة تنظيم التعيينات
                    $roomAssignments = [];
                    foreach ($assignments as $assignment) {
                        $roomAssignments[$assignment['room_number']][$assignment['guard_type']][] = $assignment;
                    }
                    
                } catch(PDOException $e) {
                    $conn->rollBack();
                    $message = 'خطأ في تحديث التعيينات: ' . $e->getMessage();
                    $messageType = 'error';
                }
                break;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تعديل جدول الحراسة - نظام إدارة حراسة الامتحانات</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .edit-sections {
            display: grid;
            gap: 30px;
        }

        .edit-section {
            background: white;
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .rooms-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .room-card {
            background: var(--light-color);
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 20px;
        }

        .room-header {
            text-align: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .room-number {
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--primary-color);
            margin-bottom: 5px;
        }

        .guard-selector {
            margin-bottom: 15px;
        }

        .guard-selector label {
            display: block;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--dark-color);
            font-size: 0.9rem;
        }

        .guard-select {
            width: 100%;
            padding: 10px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            font-family: 'Cairo', sans-serif;
            font-size: 0.9rem;
        }

        .guard-select.main-guard {
            border-color: var(--danger-color);
            background: #fef2f2;
        }

        .current-assignments {
            background: #f0fdf4;
            border: 1px solid var(--success-color);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .current-assignments h4 {
            color: var(--success-color);
            margin-bottom: 10px;
        }

        .assignment-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: white;
            padding: 8px 12px;
            margin-bottom: 8px;
            border-radius: 6px;
            border: 1px solid var(--border-color);
        }

        .assignment-info {
            font-weight: 600;
            color: var(--dark-color);
            font-size: 0.85rem;
        }

        .assignment-rank {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.7rem;
            font-weight: 600;
        }

        .assignment-rank.main {
            background: var(--danger-color);
            color: white;
        }

        .assignment-rank.regular {
            background: var(--primary-color);
            color: white;
        }

        .tabs {
            display: flex;
            border-bottom: 2px solid var(--border-color);
            margin-bottom: 20px;
        }

        .tab {
            padding: 12px 24px;
            background: var(--light-color);
            border: none;
            border-bottom: 3px solid transparent;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            color: #666;
            transition: var(--transition);
        }

        .tab.active {
            background: white;
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✏️ تعديل جدول الحراسة</h1>
            <p>تعديل معلومات وتعيينات جدول الحراسة</p>
            <a href="index.php" class="btn btn-secondary">← العودة لقائمة الجداول</a>
        </div>

        <?php if ($message): ?>
        <div class="alert alert-<?= $messageType ?>">
            <?= htmlspecialchars($message) ?>
        </div>
        <?php endif; ?>

        <div class="tabs">
            <button class="tab active" onclick="showTab('basic-info')">📋 المعلومات الأساسية</button>
            <button class="tab" onclick="showTab('assignments')">👥 تعيينات الحراس</button>
        </div>

        <!-- تبويب المعلومات الأساسية -->
        <div id="basic-info" class="tab-content active">
            <div class="edit-section">
                <h3 class="section-title">📋 تعديل المعلومات الأساسية</h3>
                
                <form method="POST">
                    <input type="hidden" name="action" value="update_basic_info">
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>اسم الجدول *</label>
                            <input type="text" name="schedule_name" class="form-control" required 
                                   value="<?= htmlspecialchars($schedule['schedule_name']) ?>">
                        </div>
                        
                        <div class="form-group">
                            <label>تاريخ الامتحان *</label>
                            <input type="date" name="exam_date" class="form-control" required
                                   value="<?= $schedule['exam_date'] ?>">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>فترة الامتحان *</label>
                            <select name="exam_period" class="form-control" required>
                                <option value="morning" <?= $schedule['exam_period'] == 'morning' ? 'selected' : '' ?>>
                                    الفترة الصباحية
                                </option>
                                <option value="evening" <?= $schedule['exam_period'] == 'evening' ? 'selected' : '' ?>>
                                    الفترة المسائية
                                </option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label>نوع الامتحان *</label>
                            <select name="exam_type" class="form-control" required>
                                <option value="bem" <?= $schedule['exam_type'] == 'bem' ? 'selected' : '' ?>>
                                    شهادة التعليم المتوسط
                                </option>
                                <option value="bac" <?= $schedule['exam_type'] == 'bac' ? 'selected' : '' ?>>
                                    شهادة البكالوريا
                                </option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>ملاحظات</label>
                        <textarea name="notes" class="form-control" rows="3" 
                                  placeholder="ملاحظات إضافية حول الجدول..."><?= htmlspecialchars($schedule['notes'] ?? '') ?></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-success btn-lg">💾 حفظ التغييرات</button>
                </form>
            </div>
        </div>

        <!-- تبويب تعيينات الحراس -->
        <div id="assignments" class="tab-content">
            <div class="edit-section">
                <h3 class="section-title">👥 تعديل تعيينات الحراس</h3>
                
                <?php if (!empty($assignments)): ?>
                <div class="current-assignments">
                    <h4>التعيينات الحالية:</h4>
                    <p>إجمالي التعيينات: <strong><?= count($assignments) ?></strong></p>
                </div>
                <?php endif; ?>
                
                <form method="POST" id="assignmentForm">
                    <input type="hidden" name="action" value="update_assignments">
                    
                    <div class="rooms-grid">
                        <?php for ($room = 1; $room <= ($centerSettings['rooms_count'] ?? 14); $room++): ?>
                        <?php 
                        $roomNumber = str_pad($room, 2, '0', STR_PAD_LEFT);
                        $currentMainGuard = null;
                        $currentGuard2 = null;
                        $currentGuard3 = null;
                        
                        // البحث عن التعيينات الحالية
                        if (isset($roomAssignments[$roomNumber])) {
                            if (isset($roomAssignments[$roomNumber]['main'][0])) {
                                $currentMainGuard = $roomAssignments[$roomNumber]['main'][0]['guard_id'];
                            }
                            if (isset($roomAssignments[$roomNumber]['regular'])) {
                                foreach ($roomAssignments[$roomNumber]['regular'] as $index => $regular) {
                                    if ($regular['guard_order'] == 2) $currentGuard2 = $regular['guard_id'];
                                    if ($regular['guard_order'] == 3) $currentGuard3 = $regular['guard_id'];
                                }
                            }
                        }
                        ?>
                        <div class="room-card">
                            <div class="room-header">
                                <div class="room-number">قاعة <?= $roomNumber ?></div>
                            </div>
                            
                            <div class="guard-selector">
                                <label>الحارس الرئيسي:</label>
                                <select name="main_guard_<?= $room ?>" class="guard-select main-guard">
                                    <option value="">-- اختر حارس رئيسي --</option>
                                    <?php foreach ($guards as $guard): ?>
                                    <option value="<?= $guard['id'] ?>" <?= $currentMainGuard == $guard['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($guard['guard_number'] . ' - ' . $guard['first_name'] . ' ' . $guard['last_name']) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="guard-selector">
                                <label>الحارس العادي الأول:</label>
                                <select name="guard2_<?= $room ?>" class="guard-select">
                                    <option value="">-- اختر حارس --</option>
                                    <?php foreach ($guards as $guard): ?>
                                    <option value="<?= $guard['id'] ?>" <?= $currentGuard2 == $guard['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($guard['guard_number'] . ' - ' . $guard['first_name'] . ' ' . $guard['last_name']) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="guard-selector">
                                <label>الحارس العادي الثاني:</label>
                                <select name="guard3_<?= $room ?>" class="guard-select">
                                    <option value="">-- اختر حارس --</option>
                                    <?php foreach ($guards as $guard): ?>
                                    <option value="<?= $guard['id'] ?>" <?= $currentGuard3 == $guard['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($guard['guard_number'] . ' - ' . $guard['first_name'] . ' ' . $guard['last_name']) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <?php endfor; ?>
                    </div>

                    <div style="text-align: center; margin-top: 30px;">
                        <button type="submit" class="btn btn-success btn-lg">💾 حفظ تعيينات الحراس</button>
                        <a href="view.php?id=<?= $scheduleId ?>" class="btn btn-primary btn-lg" style="margin-right: 15px;">👁️ عرض الجدول</a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // إزالة التفعيل من جميع الأزرار
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // إظهار التبويب المحدد
            document.getElementById(tabName).classList.add('active');
            
            // تفعيل الزر المحدد
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
