<?php
/**
 * صفحة إعدادات النظام
 */

require_once '../../config/database.php';

$database = new Database();
$conn = $database->getConnection();

$message = '';
$messageType = '';

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action']) && $_POST['action'] == 'save_settings') {
        try {
            $stmt = $conn->prepare("
                UPDATE center_settings SET 
                    center_name = ?, 
                    center_code = ?, 
                    head_name = ?, 
                    exam_type = ?, 
                    rooms_count = ?, 
                    guards_per_room = ?, 
                    candidates_count = ?, 
                    candidates_per_room = ?,
                    updated_at = NOW()
                WHERE id = 1
            ");
            
            $stmt->execute([
                $_POST['center_name'],
                $_POST['center_code'],
                $_POST['head_name'],
                $_POST['exam_type'],
                intval($_POST['rooms_count']),
                intval($_POST['guards_per_room']),
                intval($_POST['candidates_count']),
                intval($_POST['candidates_per_room'])
            ]);
            
            $message = 'تم حفظ الإعدادات بنجاح';
            $messageType = 'success';
            
        } catch(PDOException $e) {
            $message = 'خطأ في حفظ الإعدادات: ' . $e->getMessage();
            $messageType = 'error';
        }
    }
}

// جلب الإعدادات الحالية
$stmt = $conn->prepare("SELECT * FROM center_settings WHERE id = 1");
$stmt->execute();
$settings = $stmt->fetch();

// إذا لم توجد إعدادات، إنشاء سجل افتراضي
if (!$settings) {
    $stmt = $conn->prepare("
        INSERT INTO center_settings (id, center_name, center_code, head_name, exam_type, rooms_count, guards_per_room, candidates_count, candidates_per_room) 
        VALUES (1, 'مركز الامتحانات', '00000', 'رئيس المركز', 'bem', 14, 3, 280, 20)
    ");
    $stmt->execute();
    
    // إعادة جلب الإعدادات
    $stmt = $conn->prepare("SELECT * FROM center_settings WHERE id = 1");
    $stmt->execute();
    $settings = $stmt->fetch();
}

// جلب إحصائيات النظام
try {
    // عدد الحراس
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM guards WHERE is_active = 1");
    $stmt->execute();
    $guardsCount = $stmt->fetch()['total'];
    
    // عدد الجداول
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM guard_schedules");
    $stmt->execute();
    $schedulesCount = $stmt->fetch()['total'];
    
    // عدد التعيينات
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM guard_assignments");
    $stmt->execute();
    $assignmentsCount = $stmt->fetch()['total'];
    
    // حجم قاعدة البيانات
    $stmt = $conn->prepare("
        SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS db_size_mb 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE()
    ");
    $stmt->execute();
    $dbSize = $stmt->fetch()['db_size_mb'] ?? 0;
    
} catch(PDOException $e) {
    $guardsCount = 0;
    $schedulesCount = 0;
    $assignmentsCount = 0;
    $dbSize = 0;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام - نظام إدارة حراسة الامتحانات</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .settings-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .settings-section {
            background: white;
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--shadow-light);
            border: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .stat-card {
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            color: white;
            padding: 20px;
            border-radius: var(--border-radius);
            text-align: center;
            box-shadow: var(--shadow-light);
        }

        .stat-card.success {
            background: linear-gradient(135deg, var(--success-color), #16a34a);
        }

        .stat-card.warning {
            background: linear-gradient(135deg, var(--accent-color), #d97706);
        }

        .stat-card.info {
            background: linear-gradient(135deg, var(--info-color), #0891b2);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .backup-section {
            background: #fef3c7;
            border: 1px solid var(--accent-color);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-top: 20px;
        }

        .backup-section h4 {
            color: #92400e;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .backup-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .danger-zone {
            background: #fee2e2;
            border: 1px solid var(--danger-color);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-top: 20px;
        }

        .danger-zone h4 {
            color: var(--danger-color);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .danger-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        @media (max-width: 768px) {
            .settings-container {
                grid-template-columns: 1fr;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚙️ إعدادات النظام</h1>
            <p>إعدادات المركز والنظام العامة</p>
            <a href="../../index.php" class="btn btn-secondary">← العودة للرئيسية</a>
        </div>

        <?php if ($message): ?>
        <div class="alert alert-<?= $messageType ?>">
            <?= htmlspecialchars($message) ?>
        </div>
        <?php endif; ?>

        <div class="settings-container">
            <!-- إعدادات المركز -->
            <div class="settings-section">
                <h3 class="section-title">🏛️ إعدادات المركز</h3>
                
                <form method="POST">
                    <input type="hidden" name="action" value="save_settings">
                    
                    <div class="form-group">
                        <label>اسم المركز *</label>
                        <input type="text" name="center_name" class="form-control" required 
                               value="<?= htmlspecialchars($settings['center_name']) ?>">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>رمز المركز *</label>
                            <input type="text" name="center_code" class="form-control" required 
                                   value="<?= htmlspecialchars($settings['center_code']) ?>">
                        </div>
                        
                        <div class="form-group">
                            <label>اسم رئيس المركز *</label>
                            <input type="text" name="head_name" class="form-control" required 
                                   value="<?= htmlspecialchars($settings['head_name']) ?>">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>نوع الامتحان الافتراضي *</label>
                            <select name="exam_type" class="form-control" required>
                                <option value="bem" <?= $settings['exam_type'] == 'bem' ? 'selected' : '' ?>>
                                    شهادة التعليم المتوسط
                                </option>
                                <option value="bac" <?= $settings['exam_type'] == 'bac' ? 'selected' : '' ?>>
                                    شهادة البكالوريا
                                </option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label>عدد القاعات *</label>
                            <input type="number" name="rooms_count" class="form-control" required min="1" max="50"
                                   value="<?= $settings['rooms_count'] ?>">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>عدد الحراس لكل قاعة *</label>
                            <input type="number" name="guards_per_room" class="form-control" required min="1" max="5"
                                   value="<?= $settings['guards_per_room'] ?>">
                        </div>
                        
                        <div class="form-group">
                            <label>عدد المترشحين الإجمالي</label>
                            <input type="number" name="candidates_count" class="form-control" min="0"
                                   value="<?= $settings['candidates_count'] ?>">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>عدد المترشحين لكل قاعة</label>
                        <input type="number" name="candidates_per_room" class="form-control" min="1" max="50"
                               value="<?= $settings['candidates_per_room'] ?>">
                    </div>
                    
                    <button type="submit" class="btn btn-success btn-lg" style="width: 100%;">
                        💾 حفظ الإعدادات
                    </button>
                </form>
            </div>

            <!-- إحصائيات النظام -->
            <div class="settings-section">
                <h3 class="section-title">📊 إحصائيات النظام</h3>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number"><?= $guardsCount ?></div>
                        <div class="stat-label">الحراس النشطين</div>
                    </div>
                    
                    <div class="stat-card success">
                        <div class="stat-number"><?= $schedulesCount ?></div>
                        <div class="stat-label">جداول الحراسة</div>
                    </div>
                    
                    <div class="stat-card warning">
                        <div class="stat-number"><?= $assignmentsCount ?></div>
                        <div class="stat-label">التعيينات</div>
                    </div>
                    
                    <div class="stat-card info">
                        <div class="stat-number"><?= $dbSize ?> MB</div>
                        <div class="stat-label">حجم قاعدة البيانات</div>
                    </div>
                </div>
                
                <div style="margin-top: 20px; padding: 15px; background: var(--light-color); border-radius: 8px;">
                    <h4 style="margin-bottom: 10px; color: var(--dark-color);">معلومات النظام</h4>
                    <div style="font-size: 0.9rem; color: #666;">
                        <p><strong>إصدار PHP:</strong> <?= PHP_VERSION ?></p>
                        <p><strong>إصدار MySQL:</strong> <?= $conn->getAttribute(PDO::ATTR_SERVER_VERSION) ?></p>
                        <p><strong>آخر تحديث للإعدادات:</strong> <?= date('Y-m-d H:i:s', strtotime($settings['updated_at'])) ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم النسخ الاحتياطي -->
        <div class="backup-section">
            <h4>💾 النسخ الاحتياطي والاستعادة</h4>
            <p style="color: #92400e; margin-bottom: 15px;">
                قم بإنشاء نسخة احتياطية من البيانات أو استعادة نسخة سابقة
            </p>
            <div class="backup-actions">
                <a href="backup.php" class="btn btn-info">📥 تحميل نسخة احتياطية</a>
                <a href="restore.php" class="btn btn-warning">📤 استعادة نسخة احتياطية</a>
                <a href="export.php" class="btn btn-secondary">📊 تصدير البيانات (Excel)</a>
            </div>
        </div>

        <!-- المنطقة الخطرة -->
        <div class="danger-zone">
            <h4>⚠️ المنطقة الخطرة</h4>
            <p style="color: var(--danger-color); margin-bottom: 15px;">
                العمليات التالية خطيرة ولا يمكن التراجع عنها. تأكد من إنشاء نسخة احتياطية أولاً.
            </p>
            <div class="danger-actions">
                <button onclick="clearAllData()" class="btn btn-danger">🗑️ مسح جميع البيانات</button>
                <button onclick="resetSettings()" class="btn btn-danger">🔄 إعادة تعيين الإعدادات</button>
                <a href="../../config/database.php" class="btn btn-danger">🔧 إعادة إنشاء قاعدة البيانات</a>
            </div>
        </div>
    </div>

    <script>
        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                if (confirm('تأكيد أخير: سيتم حذف جميع الحراس والجداول والتعيينات. هل تريد المتابعة؟')) {
                    window.location.href = 'clear_data.php';
                }
            }
        }

        function resetSettings() {
            if (confirm('هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟')) {
                window.location.href = 'reset_settings.php';
            }
        }

        // تحديث تلقائي للإحصائيات كل 30 ثانية
        setInterval(function() {
            location.reload();
        }, 30000);
    </script>
</body>
</html>
